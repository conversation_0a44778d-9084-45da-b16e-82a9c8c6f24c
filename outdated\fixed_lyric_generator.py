"""
修复版歌词视频生成器
解决PIL和MoviePy兼容性问题
"""

import os
import re
from typing import List, Tuple, Dict, Optional, Union
from moviepy.editor import *
from PIL import Image, ImageDraw, ImageFont
import numpy as np


class FixedLyricVideoGenerator:
    """修复版歌词视频生成器类"""
    
    def __init__(self, width: int = 1920, height: int = 1080, fps: int = 30):
        """
        初始化生成器
        
        Args:
            width: 视频宽度
            height: 视频高度  
            fps: 帧率
        """
        self.width = width
        self.height = height
        self.fps = fps
        self.default_font_size = 80
        self.default_font_color = 'white'
        self.highlight_color = 'yellow'
        
    def parse_lrc_file(self, lrc_path: str) -> List[Tuple[float, str]]:
        """
        解析LRC歌词文件
        
        Args:
            lrc_path: LRC文件路径
            
        Returns:
            歌词时间戳和文本的列表
        """
        lyrics = []
        
        with open(lrc_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for line in lines:
            line = line.strip()
            # 匹配时间戳格式 [mm:ss.xx]
            time_match = re.match(r'\[(\d{2}):(\d{2})\.(\d{2})\](.*)', line)
            if time_match:
                minutes = int(time_match.group(1))
                seconds = int(time_match.group(2))
                centiseconds = int(time_match.group(3))
                text = time_match.group(4).strip()
                
                # 转换为总秒数
                timestamp = minutes * 60 + seconds + centiseconds / 100
                
                if text:  # 只添加非空文本
                    lyrics.append((timestamp, text))
                    
        return sorted(lyrics, key=lambda x: x[0])
    
    def create_text_clip(self, text: str, start_time: float, duration: float, 
                        is_highlighted: bool = False, position: Union[str, Tuple] = 'center',
                        font_size: Optional[int] = None, animation: str = 'none') -> TextClip:
        """
        创建文本片段
        
        Args:
            text: 文本内容
            start_time: 开始时间
            duration: 持续时间
            is_highlighted: 是否高亮显示
            position: 文本位置
            font_size: 字体大小
            animation: 动画效果 ('none', 'fade', 'slide', 'bounce')
            
        Returns:
            文本片段对象
        """
        if font_size is None:
            font_size = self.default_font_size
            
        color = self.highlight_color if is_highlighted else self.default_font_color
        
        # 创建文本片段
        txt_clip = TextClip(text, 
                           fontsize=font_size,
                           color=color,
                           font='Arial-Bold',
                           stroke_color='black',
                           stroke_width=2)
        
        # 设置位置
        txt_clip = txt_clip.set_position(position)
        
        # 应用动画效果
        if animation == 'fade':
            txt_clip = txt_clip.crossfadein(0.3).crossfadeout(0.3)
        elif animation == 'slide':
            # 从左侧滑入
            txt_clip = txt_clip.set_position(lambda t: ('center' if t > 0.5 else (max(-txt_clip.w, -txt_clip.w + (txt_clip.w + self.width/2) * t/0.5), 'center')))
        elif animation == 'bounce':
            # 弹跳效果
            def bounce_pos(t):
                if t < 0.3:
                    y_offset = -50 * (1 - (t/0.3)) * np.sin(t/0.3 * np.pi * 4)
                    return ('center', self.height/2 + y_offset)
                return 'center'
            txt_clip = txt_clip.set_position(bounce_pos)
        
        # 设置时间
        txt_clip = txt_clip.set_start(start_time).set_duration(duration)
        
        return txt_clip
    
    def create_background(self, background_source: str, duration: float) -> VideoClip:
        """
        创建背景视频或图片
        
        Args:
            background_source: 背景文件路径或颜色
            duration: 持续时间
            
        Returns:
            背景视频片段
        """
        if background_source.startswith('#') or background_source.lower() in ['black', 'white', 'blue', 'red', 'green']:
            # 纯色背景
            background = ColorClip(size=(self.width, self.height), 
                                 color=background_source, 
                                 duration=duration)
        elif os.path.exists(background_source):
            if background_source.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                # 视频背景
                try:
                    background = VideoFileClip(background_source)
                    if background.duration < duration:
                        background = background.loop(duration=duration)
                    else:
                        background = background.subclip(0, duration)
                    background = background.resize((self.width, self.height))
                except Exception as e:
                    print(f"视频背景加载失败，使用黑色背景: {e}")
                    background = ColorClip(size=(self.width, self.height), 
                                         color='black', 
                                         duration=duration)
            else:
                # 图片背景 - 使用更兼容的方法
                try:
                    # 先用PIL加载图片，然后转换为numpy数组
                    img = Image.open(background_source)
                    img = img.resize((self.width, self.height), Image.Resampling.LANCZOS)
                    
                    # 转换为RGB模式
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    # 转换为numpy数组
                    img_array = np.array(img)
                    
                    # 创建ImageClip
                    background = ImageClip(img_array, duration=duration)
                    
                except Exception as e:
                    print(f"图片背景加载失败，使用黑色背景: {e}")
                    background = ColorClip(size=(self.width, self.height), 
                                         color='black', 
                                         duration=duration)
        else:
            # 默认黑色背景
            background = ColorClip(size=(self.width, self.height), 
                                 color='black', 
                                 duration=duration)
        
        return background
    
    def generate_video(self, lyrics: List[Tuple[float, str]], 
                      audio_path: str,
                      background_source: str = 'black',
                      output_path: str = 'lyric_video.mp4',
                      animation: str = 'fade',
                      show_current_and_next: bool = True) -> None:
        """
        生成歌词视频
        
        Args:
            lyrics: 歌词列表 [(时间戳, 文本), ...]
            audio_path: 音频文件路径
            background_source: 背景源（文件路径或颜色）
            output_path: 输出视频路径
            animation: 动画效果
            show_current_and_next: 是否同时显示当前和下一句歌词
        """
        print("正在生成歌词视频...")
        
        # 加载音频
        audio = AudioFileClip(audio_path)
        duration = audio.duration
        
        # 创建背景
        background = self.create_background(background_source, duration)
        
        # 创建文本片段列表
        text_clips = []
        
        for i, (start_time, text) in enumerate(lyrics):
            # 计算当前歌词的结束时间
            if i < len(lyrics) - 1:
                end_time = lyrics[i + 1][0]
            else:
                end_time = duration
            
            lyric_duration = end_time - start_time
            
            if show_current_and_next:
                # 当前歌词（高亮）
                current_clip = self.create_text_clip(
                    text, start_time, lyric_duration,
                    is_highlighted=True,
                    position=('center', self.height/2 - 60),
                    animation=animation
                )
                text_clips.append(current_clip)
                
                # 下一句歌词（普通显示）
                if i < len(lyrics) - 1:
                    next_text = lyrics[i + 1][1]
                    next_clip = self.create_text_clip(
                        next_text, start_time, lyric_duration,
                        is_highlighted=False,
                        position=('center', self.height/2 + 60),
                        font_size=self.default_font_size - 20
                    )
                    text_clips.append(next_clip)
            else:
                # 只显示当前歌词
                current_clip = self.create_text_clip(
                    text, start_time, lyric_duration,
                    is_highlighted=True,
                    position='center',
                    animation=animation
                )
                text_clips.append(current_clip)
        
        # 合成最终视频
        print("正在合成视频...")
        final_video = CompositeVideoClip([background] + text_clips)
        final_video = final_video.set_audio(audio)
        final_video = final_video.set_fps(self.fps)
        
        # 输出视频
        print(f"正在导出视频到: {output_path}")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )
        
        print("歌词视频生成完成！")


def main():
    """演示用法"""
    print("修复版歌词视频生成器测试")
    print("=" * 50)
    
    # 创建生成器实例
    generator = FixedLyricVideoGenerator(width=1280, height=720, fps=24)
    
    # 示例歌词数据
    sample_lyrics = [
        (0.0, "这是修复版生成器"),
        (3.0, "解决了兼容性问题"),
        (6.0, "现在应该可以正常工作"),
        (9.0, "支持图片和视频背景"),
        (12.0, "感谢测试")
    ]
    
    print("修复的问题:")
    print("1. PIL.Image.ANTIALIAS 兼容性问题")
    print("2. ImageClip 加载图片的问题")
    print("3. 类型注解和错误处理")
    print("4. 背景图片尺寸调整")
    
    print("\n可以安全使用此版本生成视频!")


if __name__ == "__main__":
    main()
