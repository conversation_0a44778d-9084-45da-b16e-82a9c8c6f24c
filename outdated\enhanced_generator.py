# filepath: e:\repos\jingwu-hero\enhanced_generator.py
"""
精武英雄歌词视频生成器 - 增强版
支持背景图片、更多动画效果和双语模式
"""

import os
import re
from typing import List, Tuple, Optional
from moviepy.editor import (
    AudioFileClip, ImageClip, CompositeVideoClip, ColorClip
)
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
import numpy as np


class EnhancedJingwuGenerator:
    """增强版精武英雄歌词视频生成器"""
    
    def __init__(self, width: int = 1920, height: int = 1080, fps: int = 30):
        self.width = width
        self.height = height
        self.fps = fps
        self.default_font_size = 80
        self.default_font_color = 'white'
        self.highlight_color = '#FFD700'  # 金色
        self.shadow_color = (0, 0, 0, 200)
        
        # 武侠风格色彩
        self.theme_colors = {
            'gold': '#FFD700',
            'red': '#DC143C',
            'dark_red': '#8B0000',
            'black': '#000000',
            'white': '#FFFFFF',
            'silver': '#C0C0C0'
        }
        
    def parse_lrc_file(self, lrc_path: str) -> List[Tuple[float, str]]:
        """解析LRC歌词文件"""
        lyrics = []
        
        with open(lrc_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for line in lines:
            line = line.strip()
            time_match = re.match(r'\[(\d{2}):(\d{2})\.(\d{2})\](.*)', line)
            if time_match:
                minutes = int(time_match.group(1))
                seconds = int(time_match.group(2))
                centiseconds = int(time_match.group(3))
                text = time_match.group(4).strip()
                
                timestamp = minutes * 60 + seconds + centiseconds / 100
                
                if text:
                    lyrics.append((timestamp, text))
                    
        return sorted(lyrics, key=lambda x: x[0])
    
    def load_background_image(self, bg_path: str) -> Optional[np.ndarray]:
        """加载并处理背景图片"""
        try:
            # 加载图片
            img = Image.open(bg_path)            # 调整尺寸
            try:
                img = img.resize((self.width, self.height), Image.Resampling.LANCZOS)
            except AttributeError:
                img = img.resize((self.width, self.height), Image.ANTIALIAS)
            
            # 降低亮度和对比度，使文字更清晰
            enhancer = ImageEnhance.Brightness(img)
            img = enhancer.enhance(0.4)  # 降低亮度
            
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(0.6)  # 降低对比度
            
            # 添加模糊效果
            img = img.filter(ImageFilter.GaussianBlur(radius=1))
            
            # 转换为numpy数组
            return np.array(img)
            
        except Exception as e:
            print(f"⚠️  背景图片加载失败: {e}")
            return None
    
    def create_gradient_background(self, color1: tuple, color2: tuple) -> np.ndarray:
        """创建渐变背景"""
        # 创建垂直渐变
        gradient = np.zeros((self.height, self.width, 3), dtype=np.uint8)
        
        for y in range(self.height):
            ratio = y / self.height
            r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
            g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
            b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
            gradient[y, :] = [r, g, b]
        
        return gradient
    
    def create_enhanced_text_image(self, text: str, font_size: int, color: str, 
                                 width: int, height: int, y_position: int,
                                 glow: bool = False) -> np.ndarray:
        """创建增强文字图像，支持发光效果"""
        # 创建高分辨率图像用于抗锯齿
        scale = 2
        scaled_width = width * scale
        scaled_height = height * scale
        scaled_font_size = font_size * scale
        
        img = Image.new('RGBA', (scaled_width, scaled_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 加载字体
        try:
            # 检测中文字符
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
            if has_chinese:
                font = ImageFont.truetype("simsun.ttc", scaled_font_size)
            else:
                try:
                    font = ImageFont.truetype("arial.ttf", scaled_font_size)
                except:
                    font = ImageFont.truetype("calibri.ttf", scaled_font_size)
        except:
            font = ImageFont.load_default()
        
        # 计算文字位置
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (scaled_width - text_width) // 2
        y = y_position * scale - text_height // 2
        
        # 发光效果
        if glow and color == '#FFD700':
            # 创建多层阴影产生发光效果
            glow_color = (255, 215, 0, 120)
            for offset in range(8, 0, -1):
                for dx in range(-offset, offset + 1):
                    for dy in range(-offset, offset + 1):
                        if dx * dx + dy * dy <= offset * offset:
                            alpha = max(0, 120 - offset * 15)
                            glow_rgba = (255, 215, 0, alpha)
                            draw.text((x + dx, y + dy), text, fill=glow_rgba, font=font)
        
        # 文字阴影
        shadow_color = (0, 0, 0, 200)
        draw.text((x + 3 * scale, y + 3 * scale), text, fill=shadow_color, font=font)
        
        # 主文字
        if color == '#FFD700':  # 金色
            main_color = (255, 215, 0, 255)
        else:  # 白色
            main_color = (255, 255, 255, 255)
            
        draw.text((x, y), text, fill=main_color, font=font)
        
        # 缩放回原始尺寸
        img = img.resize((width, height), Image.Resampling.LANCZOS)
        
        # 转换为RGB
        img_rgb = Image.new('RGB', (width, height), (0, 0, 0))
        img_rgb.paste(img, (0, 0), img)
        
        return np.array(img_rgb)
    
    def create_lyric_clip_with_animation(self, text: str, start_time: float, duration: float,
                                       is_highlighted: bool = False, y_position: int = None,
                                       animation: str = 'fade') -> ImageClip:
        """创建带动画效果的歌词片段"""
        if y_position is None:
            y_position = self.height // 2
            
        font_size = self.default_font_size if is_highlighted else self.default_font_size - 20
        color = self.highlight_color if is_highlighted else self.default_font_color
        
        # 创建文字图像
        text_img = self.create_enhanced_text_image(
            text, font_size, color, self.width, self.height, y_position,
            glow=is_highlighted
        )
        
        # 创建ImageClip
        clip = ImageClip(text_img, duration=duration)
        clip = clip.set_start(start_time)
        
        # 根据动画类型添加效果
        if animation == 'fade':
            if duration > 0.6:
                clip = clip.crossfadein(0.3).crossfadeout(0.3)
        elif animation == 'slide':
            # 从左滑入
            clip = clip.set_position(lambda t: (-self.width + int(t * self.width / 0.5), 'center') if t < 0.5 else ('center', 'center'))
        elif animation == 'zoom':
            # 缩放效果
            if is_highlighted:
                clip = clip.resize(lambda t: 0.8 + 0.2 * min(t / 0.3, 1))
        
        return clip
    
    def generate_enhanced_video(self, lyrics: List[Tuple[float, str]], 
                              audio_path: str, output_path: str,
                              background_image: str = None,
                              animation_style: str = 'fade') -> bool:
        """生成增强版歌词视频"""
        try:
            print(f"🎬 开始生成增强版: {output_path}")
            
            # 加载音频
            print("📻 加载音频...")
            audio = AudioFileClip(audio_path)
            duration = audio.duration
            print(f"   音频时长: {duration:.1f} 秒")
            
            # 创建背景
            print("🎨 创建背景...")
            if background_image and os.path.exists(background_image):
                bg_array = self.load_background_image(background_image)
                if bg_array is not None:
                    background = ImageClip(bg_array, duration=duration)
                    print(f"   使用背景图片: {background_image}")
                else:
                    # fallback to gradient
                    gradient_bg = self.create_gradient_background((139, 0, 0), (0, 0, 0))
                    background = ImageClip(gradient_bg, duration=duration)
                    print("   使用渐变背景")
            else:
                # 渐变背景
                gradient_bg = self.create_gradient_background((139, 0, 0), (0, 0, 0))
                background = ImageClip(gradient_bg, duration=duration)
                print("   使用渐变背景")
            
            # 创建歌词片段
            print("📝 创建歌词片段...")
            clips = [background]
            
            for i, (start_time, text) in enumerate(lyrics):
                # 计算结束时间
                if i < len(lyrics) - 1:
                    end_time = lyrics[i + 1][0]
                else:
                    end_time = duration
                
                lyric_duration = end_time - start_time
                
                # 创建当前歌词（高亮）
                current_clip = self.create_lyric_clip_with_animation(
                    text, start_time, lyric_duration,
                    is_highlighted=True,
                    y_position=self.height // 2 - 50,
                    animation=animation_style
                )
                clips.append(current_clip)
                
                # 创建下一句歌词（预览）
                if i < len(lyrics) - 1:
                    next_text = lyrics[i + 1][1]
                    next_clip = self.create_lyric_clip_with_animation(
                        next_text, start_time, lyric_duration,
                        is_highlighted=False,
                        y_position=self.height // 2 + 80,
                        animation='fade'
                    )
                    clips.append(next_clip)
            
            print(f"   创建了 {len(clips)} 个片段")
            
            # 合成视频
            print("🎞️  合成视频...")
            final_video = CompositeVideoClip(clips)
            final_video = final_video.set_audio(audio)
            final_video = final_video.set_fps(self.fps)
            
            # 导出视频
            print("💾 导出视频...")
            final_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None,
                preset='medium',
                ffmpeg_params=['-crf', '18']  # 高质量
            )
            
            print("✅ 增强版视频生成成功！")
            return True
            
        except Exception as e:
            print(f"❌ 生成失败: {e}")
            return False
    
    def generate_bilingual_video(self, chinese_lyrics: List[Tuple[float, str]],
                               english_lyrics: List[Tuple[float, str]],
                               audio_path: str, output_path: str,
                               background_image: str = None) -> bool:
        """生成双语版本视频"""
        try:
            print(f"🎬 开始生成双语版: {output_path}")
            
            # 加载音频
            audio = AudioFileClip(audio_path)
            duration = audio.duration
            
            # 创建背景
            if background_image and os.path.exists(background_image):
                bg_array = self.load_background_image(background_image)
                if bg_array is not None:
                    background = ImageClip(bg_array, duration=duration)
                else:
                    gradient_bg = self.create_gradient_background((139, 0, 0), (0, 0, 0))
                    background = ImageClip(gradient_bg, duration=duration)
            else:
                gradient_bg = self.create_gradient_background((139, 0, 0), (0, 0, 0))
                background = ImageClip(gradient_bg, duration=duration)
            
            clips = [background]
            
            # 处理中文歌词
            print("📝 处理中文歌词...")
            for i, (start_time, text) in enumerate(chinese_lyrics):
                if i < len(chinese_lyrics) - 1:
                    end_time = chinese_lyrics[i + 1][0]
                else:
                    end_time = duration
                
                lyric_duration = end_time - start_time
                
                # 中文歌词在上方
                chinese_clip = self.create_lyric_clip_with_animation(
                    text, start_time, lyric_duration,
                    is_highlighted=True,
                    y_position=self.height // 2 - 80,
                    animation='fade'
                )
                clips.append(chinese_clip)
            
            # 处理英文歌词
            print("📝 处理英文歌词...")
            for i, (start_time, text) in enumerate(english_lyrics):
                if i < len(english_lyrics) - 1:
                    end_time = english_lyrics[i + 1][0]
                else:
                    end_time = duration
                
                lyric_duration = end_time - start_time
                
                # 英文歌词在下方
                english_clip = self.create_lyric_clip_with_animation(
                    text, start_time, lyric_duration,
                    is_highlighted=False,
                    y_position=self.height // 2 + 60,
                    animation='fade'
                )
                clips.append(english_clip)
            
            print(f"   创建了 {len(clips)} 个片段")
            
            # 合成视频
            final_video = CompositeVideoClip(clips)
            final_video = final_video.set_audio(audio)
            final_video = final_video.set_fps(self.fps)
            
            # 导出视频
            final_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None,
                preset='medium',
                ffmpeg_params=['-crf', '18']
            )
            
            print("✅ 双语版视频生成成功！")
            return True
            
        except Exception as e:
            print(f"❌ 双语版生成失败: {e}")
            return False


def main():
    """主函数 - 演示增强功能"""
    print("🥋 精武英雄歌词视频生成器 - 增强版")
    print("=" * 60)
    print("🚀 新功能:")
    print("   • 背景图片支持")
    print("   • 发光文字效果")
    print("   • 渐变背景")
    print("   • 多种动画效果")
    print("   • 双语模式")
    print()
    
    # 文件检查
    audio_file = "精武英雄 - 甄子丹.flac"
    chinese_lyrics = "精武英雄 - 甄子丹.lrc"
    english_lyrics = "Jingwu Hero - Donnie Yen (English).lrc"
    background_img = "jingwu_hero_background.jpg"
    
    print("📋 文件检查:")
    files_status = {
        "音频文件": audio_file,
        "中文歌词": chinese_lyrics,
        "英文歌词": english_lyrics,
        "背景图片": background_img
    }
    
    for name, file in files_status.items():
        if os.path.exists(file):
            print(f"   ✅ {name}: {file}")
        else:
            print(f"   ❌ {name}: {file}")
    
    # 创建生成器
    generator = EnhancedJingwuGenerator(width=1920, height=1080, fps=30)
    
    print("\n🎯 可用的生成选项:")
    print("1. 增强版中文歌词视频（带背景图片）")
    print("2. 双语版歌词视频")
    print("3. 渐变背景版本")
    
    return generator


if __name__ == "__main__":
    main()
