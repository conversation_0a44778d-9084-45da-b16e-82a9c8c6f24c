# filepath: e:\repos\jingwu-hero\demo_enhanced.py
"""
演示增强版精武英雄歌词视频生成器的功能
"""

from enhanced_generator import EnhancedJingwuGenerator
import os


def demo_enhanced_features():
    """演示增强功能"""
    print("🎬 精武英雄歌词视频 - 增强版演示")
    print("=" * 50)
    
    # 检查文件
    files = {
        "audio": "精武英雄 - 甄子丹.flac",
        "chinese_lrc": "精武英雄 - 甄子丹.lrc",
        "english_lrc": "Jingwu Hero - Donnie Yen (English).lrc",
        "background": "jingwu_hero_background.jpg"
    }
    
    print("📋 文件检查:")
    available_files = {}
    for key, file in files.items():
        if os.path.exists(file):
            print(f"   ✅ {file}")
            available_files[key] = file
        else:
            print(f"   ❌ {file}")
    
    if "audio" not in available_files or "chinese_lrc" not in available_files:
        print("\n❌ 缺少必要文件，无法继续")
        return
    
    # 创建增强版生成器
    generator = EnhancedJingwuGenerator(width=1280, height=720, fps=24)
    generator.default_font_size = 60
    
    print("\n🎯 开始演示...")
    
    # 解析中文歌词 - 只用前30秒测试
    chinese_lyrics = generator.parse_lrc_file(available_files["chinese_lrc"])
    test_lyrics = [(t, text) for t, text in chinese_lyrics if t <= 30.0]
    
    print(f"📖 测试歌词（前30秒，共{len(test_lyrics)}行）")
    
    # 演示1: 增强版中文视频（带背景）
    if "background" in available_files:
        print("\n🎨 演示1: 增强版中文视频（带背景图片）")
        success1 = generator.generate_enhanced_video(
            main_lyrics=test_lyrics,
            audio_path=available_files["audio"],
            output_path="精武英雄_增强版_测试.mp4",
            background_image=available_files["background"],
            animation_style="fade"
        )
        if success1:
            print("✅ 增强版测试完成!")
    
    # 演示2: 渐变背景版本
    print("\n🌈 演示2: 渐变背景版本")
    success2 = generator.generate_enhanced_video(
        main_lyrics=test_lyrics,
        audio_path=available_files["audio"],
        output_path="精武英雄_渐变背景_测试.mp4",
        background_image=None,  # 不使用背景图片
        animation_style="zoom"
    )
    if success2:
        print("✅ 渐变背景测试完成!")
    
    # 演示3: 双语版本（如果有英文歌词）
    if "english_lrc" in available_files:
        print("\n🌍 演示3: 双语版本")
        english_lyrics = generator.parse_lrc_file(available_files["english_lrc"])
        test_english = [(t, text) for t, text in english_lyrics if t <= 30.0]
        
        success3 = generator.generate_bilingual_video(
            main_lyrics=test_lyrics,
            aux_lyrics=test_english,
            audio_path=available_files["audio"],
            output_path="精武英雄_双语版_测试.mp4",
            background_image=available_files.get("background")
        )
        if success3:
            print("✅ 双语版测试完成!")
    
    print("\n🎉 演示完成！")
    print("\n📁 生成的测试文件:")
    test_outputs = [
        "精武英雄_增强版_测试.mp4",
        "精武英雄_渐变背景_测试.mp4", 
        "精武英雄_双语版_测试.mp4"
    ]
    
    for output in test_outputs:
        if os.path.exists(output):
            print(f"   ✅ {output}")
        else:
            print(f"   ❌ {output}")


def generate_full_enhanced_versions():
    """生成完整的增强版本"""
    print("🚀 生成完整增强版本")
    print("=" * 50)
    
    generator = EnhancedJingwuGenerator(width=1920, height=1080, fps=30)
    
    # 解析完整歌词
    chinese_lyrics = generator.parse_lrc_file("精武英雄 - 甄子丹.lrc")
    print(f"📖 中文歌词: {len(chinese_lyrics)} 行")
    
    # 生成高质量完整版
    print("\n🏆 生成高质量完整版...")
    success = generator.generate_enhanced_video(
        main_lyrics=chinese_lyrics,
        audio_path="精武英雄 - 甄子丹.flac",
        output_path="精武英雄_高质量完整版.mp4",
        background_image="jingwu_hero_background.jpg",
        animation_style="fade"
    )
    
    if success:
        print("✅ 高质量完整版生成成功！")
        print("📁 输出: 精武英雄_高质量完整版.mp4")
    
    # 生成双语完整版（如果有英文歌词）
    if os.path.exists("Jingwu Hero - Donnie Yen (English).lrc"):
        print("\n🌍 生成双语完整版...")
        english_lyrics = generator.parse_lrc_file("Jingwu Hero - Donnie Yen (English).lrc")
        
        success2 = generator.generate_bilingual_video(
            main_lyrics=chinese_lyrics,
            aux_lyrics=english_lyrics,
            audio_path="精武英雄 - 甄子丹.flac",
            output_path="精武英雄_双语完整版.mp4",
            background_image="jingwu_hero_background.jpg"
        )
        
        if success2:
            print("✅ 双语完整版生成成功！")
            print("📁 输出: 精武英雄_双语完整版.mp4")


if __name__ == "__main__":
    print("🥋 精武英雄歌词视频 - 增强版演示程序")
    print("=" * 60)
    print("🎯 选择操作:")
    print("1. 运行演示（生成测试视频）")
    print("2. 生成完整版本")
    print()
    
    # 默认运行演示
    demo_enhanced_features()
    
    print("\n" + "=" * 60)
    print("💡 如果演示效果满意，可以运行:")
    print("python -c \"from demo_enhanced import generate_full_enhanced_versions; generate_full_enhanced_versions()\"")
