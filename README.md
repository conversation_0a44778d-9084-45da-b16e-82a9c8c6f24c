# 歌词视频生成器 (Lyric Video Generator)

一个功能强大的Python歌词视频生成工具，支持同步歌词显示、多种动画效果、自定义背景和字体样式。

## 🌟 主要功能

- ✅ **LRC格式支持**: 自动解析标准LRC歌词文件
- ✅ **手动歌词输入**: 支持编程方式精确控制歌词时间戳
- ✅ **多种动画效果**: fade(淡入淡出)、slide(滑动)、bounce(弹跳)等
- ✅ **灵活背景支持**: 纯色、图片、视频背景
- ✅ **自定义样式**: 字体大小、颜色、位置等可调
- ✅ **双行显示**: 支持当前歌词高亮+下一句预览
- ✅ **高质量输出**: 支持1080p、720p等多种分辨率

## 📦 安装依赖

```bash
# 安装必要的Python库
pip install moviepy==1.0.3 pillow numpy

# 可选：安装scipy用于音频处理演示
pip install scipy
```

## 🚀 快速开始

### 基础用法

```python
from lyric_video_generator import LyricVideoGenerator

# 创建生成器
generator = LyricVideoGenerator(width=1280, height=720, fps=24)

# 方法1: 使用LRC文件
lyrics = generator.parse_lrc_file('song.lrc')

# 方法2: 手动输入歌词
lyrics = [
    (0.0, "第一句歌词"),
    (3.5, "第二句歌词"),
    (7.0, "第三句歌词")
]

# 生成视频
generator.generate_video(
    lyrics=lyrics,
    audio_path="song.mp3",
    background_source="background.jpg",
    output_path="lyric_video.mp4",
    animation="fade"
)
```

### 高级自定义

```python
# 创建自定义样式的生成器
generator = LyricVideoGenerator(width=1920, height=1080, fps=30)

# 自定义文字样式
generator.default_font_size = 80
generator.default_font_color = 'white'
generator.highlight_color = '#FFD700'  # 金色高亮

# 生成高级视频
generator.generate_video(
    lyrics=lyrics,
    audio_path="song.mp3",
    background_source="background_video.mp4",  # 视频背景
    output_path="professional_video.mp4",
    animation="bounce",
    show_current_and_next=True  # 双行显示模式
)
```

## 📄 LRC文件格式

```lrc
[00:00.00]第一句歌词
[00:03.50]第二句歌词
[00:07.20]第三句歌词
[00:11.80]最后一句歌词
```

## 🎨 支持的功能

### 动画效果
- `none`: 无动画
- `fade`: 淡入淡出效果
- `slide`: 从左侧滑入
- `bounce`: 弹跳动画

### 背景类型
- **纯色**: `'black'`, `'white'`, `'#FF0000'`
- **图片**: `'background.jpg'`, `'image.png'`
- **视频**: `'background.mp4'`, `'video.avi'`

### 视频设置
- **分辨率**: 1920x1080, 1280x720, 自定义
- **帧率**: 24fps, 30fps, 60fps
- **编码**: H.264 + AAC

## 📁 项目文件

```
lyric-video-generator/
├── lyric_video_generator.py    # 主要生成器类
├── test_lyric_generator.py     # 基础测试脚本
├── simple_demo.py              # 简化演示脚本
├── advanced_demo.py            # 高级功能演示
├── quick_test.py              # 快速测试代码
├── demo_lyrics.lrc            # 示例歌词文件
├── demo_background.jpg        # 示例背景图片
└── README.md                  # 项目说明文档
```

## 🔧 API参考

### LyricVideoGenerator类

```python
class LyricVideoGenerator:
    def __init__(self, width=1920, height=1080, fps=30):
        """初始化生成器"""
        
    def parse_lrc_file(self, lrc_path):
        """解析LRC歌词文件"""
        
    def generate_video(self, lyrics, audio_path, background_source='black', 
                      output_path='lyric_video.mp4', animation='fade',
                      show_current_and_next=True):
        """生成歌词视频"""
```

### 主要参数说明

- `lyrics`: 歌词列表 `[(时间戳, 文本), ...]`
- `audio_path`: 音频文件路径
- `background_source`: 背景源（颜色/图片/视频路径）
- `output_path`: 输出视频路径
- `animation`: 动画效果类型
- `show_current_and_next`: 是否显示双行歌词

## 🎯 使用示例

### 运行演示

```bash
# 查看基础功能
python test_lyric_generator.py

# 查看完整演示
python simple_demo.py

# 运行高级功能演示
python advanced_demo.py
```

### 批量处理

```python
# 批量生成多个歌词视频
songs = [
    ("song1.mp3", "lyrics1.lrc", "bg1.jpg"),
    ("song2.mp3", "lyrics2.lrc", "bg2.jpg"),
]

generator = LyricVideoGenerator()
for i, (audio, lrc, bg) in enumerate(songs):
    lyrics = generator.parse_lrc_file(lrc)
    generator.generate_video(
        lyrics=lyrics,
        audio_path=audio,
        background_source=bg,
        output_path=f"video_{i+1}.mp4"
    )
```

## ⚠️ 注意事项

1. **首次使用**: 可能需要下载ffmpeg，请确保网络连接正常
2. **音频格式**: 支持.mp3、.wav、.m4a、.aac等常见格式
3. **生成时间**: 视频长度和分辨率影响生成速度
4. **字体支持**: 默认使用Arial字体，支持中文需要指定中文字体
5. **内存使用**: 高分辨率视频生成需要较多内存

## 🐛 常见问题

**Q: 提示缺少ffmpeg？**
A: MoviePy会自动下载ffmpeg，请确保网络连接正常。

**Q: 中文歌词显示异常？**
A: 确保LRC文件使用UTF-8编码，或在代码中指定中文字体。

**Q: 视频生成失败？**
A: 检查音频文件路径是否正确，确认文件格式受支持。

**Q: 如何提高生成速度？**
A: 降低视频分辨率和帧率，使用较短的音频文件测试。

## 📈 后续扩展

- [ ] 支持更多字体和文字效果
- [ ] 添加更多动画类型
- [ ] 支持歌词逐字高亮
- [ ] GUI界面开发
- [ ] 批量处理界面
- [ ] 模板系统

## 📝 许可证

本项目为开源项目，仅供学习和研究使用。

## 🙏 致谢

感谢MoviePy、Pillow等开源库的支持，让视频处理变得更加简单。

---

**开始创建您的第一个歌词视频吧！** 🎵✨
