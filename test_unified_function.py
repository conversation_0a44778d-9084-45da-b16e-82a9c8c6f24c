#!/usr/bin/env python3
"""
测试统一后的 generate_bilingual_video 函数
验证它能正确处理增强模式（aux_lyrics=None）和双语模式（aux_lyrics提供）
"""

import os
from enhanced_generator import EnhancedJingwuGenerator

def test_unified_function():
    """测试统一后的函数"""
    print("🧪 测试统一后的 generate_bilingual_video 函数")
    print("=" * 60)

    t_max_sec = 45.0
    
    # 检查必需文件
    audio_file = "精武英雄/精武英雄 - 甄子丹.flac"
    chinese_lrc = "精武英雄/精武英雄 - 甄子丹.lrc"
    english_lrc = "精武英雄/Jingwu Hero - Donnie Yen.lrc"
    background_img = "精武英雄/bg_v.png"
    
    print("📋 文件检查:")
    files = {
        "音频文件": audio_file,
        "中文歌词": chinese_lrc,
        "英文歌词": english_lrc,
        "背景图片": background_img
    }
    
    for name, path in files.items():
        status = "✅" if os.path.exists(path) else "❌"
        print(f"   {status} {name}: {path}")
    
    if not os.path.exists(audio_file) or not os.path.exists(chinese_lrc):
        print("\n❌ 缺少必需文件，无法进行测试")
        return False
    
    # 创建生成器
    generator = EnhancedJingwuGenerator(width=720, height=1280, fps=24)
    generator.default_font_size = 60
    
    # 解析歌词（只用前30秒测试）
    chinese_lyrics = generator.parse_lrc_file(chinese_lrc)
    test_chinese = [(t, text) for t, text in chinese_lyrics]
    
    # 测试1: 增强模式（aux_lyrics=None）
    print("\n🎨 测试1: 增强模式（aux_lyrics=None）")
    success1 = generator.generate_bilingual_video(
        main_lyrics=test_chinese,
        aux_lyrics=None,  # 增强模式
        audio_path=audio_file,
        output_path="test_enhanced_mode.mp4",
        background_image=background_img if os.path.exists(background_img) else None,
        animation_style="fade",
        t_max_sec=t_max_sec
    )
    
    if success1:
        print("✅ 增强模式测试成功！")
    else:
        print("❌ 增强模式测试失败！")
    
    # 测试2: 双语模式（如果有英文歌词）
    if os.path.exists(english_lrc):
        print("\n🌍 测试2: 双语模式（提供aux_lyrics）")
        english_lyrics = generator.parse_lrc_file(english_lrc)
        test_english = [(t, text) for t, text in english_lyrics]
        
        success2 = generator.generate_bilingual_video(
            main_lyrics=test_chinese,
            aux_lyrics=test_english,  # 双语模式
            audio_path=audio_file,
            output_path="test_bilingual_mode.mp4",
            background_image=background_img if os.path.exists(background_img) else None,
            animation_style="fade",
            t_max_sec=t_max_sec
        )
        
        if success2:
            print("✅ 双语模式测试成功！")
        else:
            print("❌ 双语模式测试失败！")
    else:
        print("\n⚠️  跳过双语模式测试（缺少英文歌词文件）")
        success2 = True  # 不影响整体测试结果
    
    # 总结
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过！统一函数工作正常")
        print("\n📁 生成的测试文件:")
        if os.path.exists("test_enhanced_mode.mp4"):
            print("   ✅ test_enhanced_mode.mp4")
        if os.path.exists("test_bilingual_mode.mp4"):
            print("   ✅ test_bilingual_mode.mp4")
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    test_unified_function()
