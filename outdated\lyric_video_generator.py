"""
歌词视频生成器
支持同步歌词显示、背景视频/图片、文字动画效果
"""

import os
import re
from typing import List, Tuple, Dict, Optional
from moviepy.editor import *
from moviepy.video.tools.drawing import color_gradient
from PIL import Image, ImageDraw, ImageFont
import numpy as np


class LyricVideoGenerator:
    """歌词视频生成器类"""
    
    def __init__(self, width: int = 1920, height: int = 1080, fps: int = 30):
        """
        初始化生成器
        
        Args:
            width: 视频宽度
            height: 视频高度  
            fps: 帧率
        """
        self.width = width
        self.height = height
        self.fps = fps
        self.default_font_size = 80
        self.default_font_color = 'white'
        self.highlight_color = 'yellow'
        
    def parse_lrc_file(self, lrc_path: str) -> List[Tuple[float, str]]:
        """
        解析LRC歌词文件
        
        Args:
            lrc_path: LRC文件路径
            
        Returns:
            歌词时间戳和文本的列表
        """
        lyrics = []
        
        with open(lrc_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for line in lines:
            line = line.strip()
            # 匹配时间戳格式 [mm:ss.xx]
            time_match = re.match(r'\[(\d{2}):(\d{2})\.(\d{2})\](.*)', line)
            if time_match:
                minutes = int(time_match.group(1))
                seconds = int(time_match.group(2))
                centiseconds = int(time_match.group(3))
                text = time_match.group(4).strip()
                
                # 转换为总秒数
                timestamp = minutes * 60 + seconds + centiseconds / 100
                
                if text:  # 只添加非空文本
                    lyrics.append((timestamp, text))
                    
        return sorted(lyrics, key=lambda x: x[0])
    
    def create_manual_lyrics(self, lyrics_data: List[Tuple[float, str]]) -> List[Tuple[float, str]]:
        """
        手动创建歌词数据
        
        Args:
            lyrics_data: [(时间戳, 歌词文本), ...]
            
        Returns:
            歌词时间戳和文本的列表
        """
        return sorted(lyrics_data, key=lambda x: x[0])
    
    def create_text_clip(self, text: str, start_time: float, duration: float, 
                        is_highlighted: bool = False, position: str = 'center',
                        font_size: Optional[int] = None, animation: str = 'none') -> TextClip:
        """
        创建文本片段
        
        Args:
            text: 文本内容
            start_time: 开始时间
            duration: 持续时间
            is_highlighted: 是否高亮显示
            position: 文本位置
            font_size: 字体大小
            animation: 动画效果 ('none', 'fade', 'slide', 'bounce')
            
        Returns:
            文本片段对象
        """
        if font_size is None:
            font_size = self.default_font_size
            
        color = self.highlight_color if is_highlighted else self.default_font_color
        
        # 创建文本片段
        txt_clip = TextClip(text, 
                           fontsize=font_size,
                           color=color,
                           font='Arial-Bold',
                           stroke_color='black',
                           stroke_width=2)
        
        # 设置位置
        txt_clip = txt_clip.set_position(position)
        
        # 应用动画效果
        if animation == 'fade':
            txt_clip = txt_clip.crossfadein(0.3).crossfadeout(0.3)
        elif animation == 'slide':
            # 从左侧滑入
            txt_clip = txt_clip.set_position(lambda t: ('center' if t > 0.5 else (max(-txt_clip.w, -txt_clip.w + (txt_clip.w + self.width/2) * t/0.5), 'center')))
        elif animation == 'bounce':
            # 弹跳效果
            def bounce_pos(t):
                if t < 0.3:
                    y_offset = -50 * (1 - (t/0.3)) * np.sin(t/0.3 * np.pi * 4)
                    return ('center', self.height/2 + y_offset)
                return 'center'
            txt_clip = txt_clip.set_position(bounce_pos)
        
        # 设置时间
        txt_clip = txt_clip.set_start(start_time).set_duration(duration)
        
        return txt_clip
    
    def create_background(self, background_source: str, duration: float) -> VideoClip:
        """
        创建背景视频或图片
        
        Args:
            background_source: 背景文件路径或颜色
            duration: 持续时间
            
        Returns:
            背景视频片段
        """
        if background_source.startswith('#') or background_source.lower() in ['black', 'white', 'blue', 'red', 'green']:
            # 纯色背景
            background = ColorClip(size=(self.width, self.height), 
                                 color=background_source, 
                                 duration=duration)
        elif os.path.exists(background_source):
            if background_source.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                # 视频背景
                background = VideoFileClip(background_source)
                if background.duration < duration:
                    # 如果视频短于需要的时长，循环播放
                    background = background.loop(duration=duration)
                else:
                    background = background.subclip(0, duration)
                background = background.resize((self.width, self.height))            else:
                # 图片背景
                try:
                    background = ImageClip(background_source, duration=duration)
                    background = background.resize((self.width, self.height))
                except Exception as e:
                    print(f"图片背景加载失败，使用黑色背景: {e}")
                    background = ColorClip(size=(self.width, self.height), 
                                         color='black', 
                                         duration=duration)
        else:
            # 默认黑色背景
            background = ColorClip(size=(self.width, self.height), 
                                 color='black', 
                                 duration=duration)
        
        return background
    
    def generate_video(self, lyrics: List[Tuple[float, str]], 
                      audio_path: str,
                      background_source: str = 'black',
                      output_path: str = 'lyric_video.mp4',
                      animation: str = 'fade',
                      show_current_and_next: bool = True) -> None:
        """
        生成歌词视频
        
        Args:
            lyrics: 歌词列表 [(时间戳, 文本), ...]
            audio_path: 音频文件路径
            background_source: 背景源（文件路径或颜色）
            output_path: 输出视频路径
            animation: 动画效果
            show_current_and_next: 是否同时显示当前和下一句歌词
        """
        print("正在生成歌词视频...")
        
        # 加载音频
        audio = AudioFileClip(audio_path)
        duration = audio.duration
        
        # 创建背景
        background = self.create_background(background_source, duration)
        
        # 创建文本片段列表
        text_clips = []
        
        for i, (start_time, text) in enumerate(lyrics):
            # 计算当前歌词的结束时间
            if i < len(lyrics) - 1:
                end_time = lyrics[i + 1][0]
            else:
                end_time = duration
            
            lyric_duration = end_time - start_time
            
            if show_current_and_next:
                # 当前歌词（高亮）
                current_clip = self.create_text_clip(
                    text, start_time, lyric_duration,
                    is_highlighted=True,
                    position=('center', self.height/2 - 60),
                    animation=animation
                )
                text_clips.append(current_clip)
                
                # 下一句歌词（普通显示）
                if i < len(lyrics) - 1:
                    next_text = lyrics[i + 1][1]
                    next_clip = self.create_text_clip(
                        next_text, start_time, lyric_duration,
                        is_highlighted=False,
                        position=('center', self.height/2 + 60),
                        font_size=self.default_font_size - 20
                    )
                    text_clips.append(next_clip)
            else:
                # 只显示当前歌词
                current_clip = self.create_text_clip(
                    text, start_time, lyric_duration,
                    is_highlighted=True,
                    position='center',
                    animation=animation
                )
                text_clips.append(current_clip)
        
        # 合成最终视频
        print("正在合成视频...")
        final_video = CompositeVideoClip([background] + text_clips)
        final_video = final_video.set_audio(audio)
        final_video = final_video.set_fps(self.fps)
        
        # 输出视频
        print(f"正在导出视频到: {output_path}")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )
        
        print("歌词视频生成完成！")


def main():
    """演示用法"""
    # 创建生成器实例
    generator = LyricVideoGenerator(width=1920, height=1080, fps=30)
    
    # 示例歌词数据（手动创建）
    sample_lyrics = [
        (0.0, "欢迎使用歌词视频生成器"),
        (3.0, "这是第一句歌词"),
        (6.0, "这是第二句歌词"),
        (9.0, "支持多种动画效果"),
        (12.0, "可以自定义背景和字体"),
        (15.0, "感谢观看这个演示")
    ]
    
    print("歌词视频生成器演示")
    print("=" * 50)
    print("支持的功能：")
    print("1. LRC歌词文件解析")
    print("2. 手动歌词数据输入")
    print("3. 多种文字动画效果")
    print("4. 背景视频/图片支持")
    print("5. 歌词同步显示")
    print("6. 自定义字体和颜色")
    print("=" * 50)
    
    # 注意：需要提供实际的音频文件路径才能生成视频
    # generator.generate_video(
    #     lyrics=sample_lyrics,
    #     audio_path="your_audio_file.mp3",
    #     background_source="black",
    #     output_path="demo_lyric_video.mp4",
    #     animation="fade"
    # )


if __name__ == "__main__":
    main()
