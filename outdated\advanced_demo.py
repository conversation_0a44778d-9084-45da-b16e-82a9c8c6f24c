"""
高级歌词视频生成器示例
演示完整的视频生成流程和高级功能
"""

from lyric_video_generator import LyricVideoGenerator
import os
from moviepy.editor import *
import numpy as np


def create_sample_audio():
    """创建一个简单的示例音频文件（用于演示）"""
    print("创建示例音频文件...")
    
    # 创建20秒的简单音频（440Hz 正弦波）
    duration = 20
    sample_rate = 44100
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # 生成多个音调的混合
    frequency1 = 440  # A4
    frequency2 = 554  # C#5  
    frequency3 = 659  # E5
    
    # 创建和弦音频
    audio_data = (np.sin(2 * np.pi * frequency1 * t) * 0.3 +
                  np.sin(2 * np.pi * frequency2 * t) * 0.2 +
                  np.sin(2 * np.pi * frequency3 * t) * 0.1)
    
    # 应用淡入淡出效果
    fade_length = int(sample_rate * 0.5)  # 0.5秒淡入淡出
    audio_data[:fade_length] *= np.linspace(0, 1, fade_length)
    audio_data[-fade_length:] *= np.linspace(1, 0, fade_length)
    
    # 保存为音频文件
    from scipy.io import wavfile
    try:
        wavfile.write('sample_audio.wav', sample_rate, (audio_data * 32767).astype(np.int16))
        print("示例音频文件已创建: sample_audio.wav")
        return True
    except ImportError:
        print("需要安装scipy来生成音频文件")
        print("使用命令: pip install scipy")
        return False


def create_background_image():
    """创建一个简单的背景图片"""
    print("创建示例背景图片...")
    
    from PIL import Image, ImageDraw
    
    # 创建渐变背景
    width, height = 1280, 720
    image = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(image)
    
    # 创建从蓝色到紫色的渐变
    for y in range(height):
        # 计算渐变比例
        ratio = y / height
        
        # 蓝色到紫色的渐变
        r = int(75 + (138 - 75) * ratio)   # 75 -> 138
        g = int(0 + (43 - 0) * ratio)     # 0 -> 43  
        b = int(130 + (226 - 130) * ratio) # 130 -> 226
        
        color = (r, g, b)
        draw.line([(0, y), (width, y)], fill=color)
    
    # 添加一些装饰圆圈
    for i in range(20):
        x = np.random.randint(0, width)
        y = np.random.randint(0, height)
        radius = np.random.randint(20, 80)
        alpha = np.random.randint(30, 100)
        
        # 创建半透明圆圈
        circle_img = Image.new('RGBA', (radius*2, radius*2), (255, 255, 255, 0))
        circle_draw = ImageDraw.Draw(circle_img)
        circle_draw.ellipse([0, 0, radius*2, radius*2], 
                           fill=(255, 255, 255, alpha))
        
        # 粘贴到主图像
        image.paste(circle_img, (x-radius, y-radius), circle_img)
    
    image.save('background.jpg', 'JPEG')
    print("示例背景图片已创建: background.jpg")


def create_advanced_lrc():
    """创建更复杂的LRC歌词文件"""
    lrc_content = """[00:00.00]欢迎使用歌词视频生成器
[00:02.50]这是一个功能强大的工具
[00:05.00]支持多种文字动画效果
[00:07.50]可以添加背景图片或视频
[00:10.00]歌词与音频完美同步
[00:12.50]自定义字体大小和颜色
[00:15.00]创建专业级歌词视频
[00:17.50]感谢您的使用和支持！
"""
    
    with open('advanced_lyrics.lrc', 'w', encoding='utf-8') as f:
        f.write(lrc_content)
    
    print("高级歌词文件已创建: advanced_lyrics.lrc")


def demo_different_animations():
    """演示不同的动画效果"""
    print("\n=== 演示不同动画效果 ===")
    
    generator = LyricVideoGenerator(width=1280, height=720, fps=24)
    
    # 创建测试歌词
    test_lyrics = [
        (0.0, "淡入淡出效果"),
        (3.0, "滑动进入效果"),
        (6.0, "弹跳动画效果"),
        (9.0, "无动画效果"),
        (12.0, "演示结束")
    ]
    
    animations = ['fade', 'slide', 'bounce', 'none', 'fade']
    
    print("动画效果对应:")
    for i, (timestamp, text) in enumerate(test_lyrics):
        animation = animations[i] if i < len(animations) else 'none'
        print(f"  {timestamp:4.1f}s: {text} - {animation}")
    
    return test_lyrics, animations


def demo_custom_styling():
    """演示自定义样式功能"""
    print("\n=== 演示自定义样式 ===")
    
    # 创建自定义样式的生成器
    generator = LyricVideoGenerator(width=1280, height=720, fps=30)
    
    # 修改默认样式
    generator.default_font_size = 60
    generator.default_font_color = 'white'
    generator.highlight_color = '#FFD700'  # 金色高亮
    
    print("自定义样式设置:")
    print(f"  字体大小: {generator.default_font_size}")
    print(f"  默认颜色: {generator.default_font_color}")
    print(f"  高亮颜色: {generator.highlight_color}")
    print(f"  视频尺寸: {generator.width}x{generator.height}")
    print(f"  帧率: {generator.fps}fps")


def generate_complete_demo():
    """生成一个完整的演示视频"""
    print("\n=== 生成完整演示视频 ===")
    
    # 检查必要文件
    has_audio = create_sample_audio()  # 需要scipy
    create_background_image()
    create_advanced_lrc()
    
    if not has_audio:
        print("跳过视频生成，因为无法创建音频文件")
        print("如果您想生成完整视频，请:")
        print("1. 安装 scipy: pip install scipy")
        print("2. 或者提供您自己的音频文件")
        return
    
    # 创建生成器
    generator = LyricVideoGenerator(width=1280, height=720, fps=24)
    
    # 自定义样式
    generator.default_font_size = 60
    generator.highlight_color = '#FFD700'
    
    # 解析歌词
    lyrics = generator.parse_lrc_file('advanced_lyrics.lrc')
    
    try:
        print("开始生成视频...")
        generator.generate_video(
            lyrics=lyrics,
            audio_path='sample_audio.wav',
            background_source='background.jpg',
            output_path='demo_lyric_video.mp4',
            animation='fade',
            show_current_and_next=True
        )
        print("✅ 演示视频生成成功: demo_lyric_video.mp4")
        
    except Exception as e:
        print(f"❌ 视频生成失败: {e}")
        print("这可能是由于缺少系统依赖或编解码器")


def show_usage_examples():
    """显示各种使用示例"""
    print("\n=== 使用示例 ===")
    
    examples = [
        {
            "title": "基础用法",
            "code": """
# 基础歌词视频
generator = LyricVideoGenerator()
lyrics = [(0.0, "第一句"), (3.0, "第二句")]
generator.generate_video(
    lyrics=lyrics,
    audio_path="song.mp3",
    output_path="output.mp4"
)"""
        },
        {
            "title": "高级自定义",
            "code": """
# 高级自定义设置
generator = LyricVideoGenerator(width=1920, height=1080, fps=30)
generator.default_font_size = 80
generator.highlight_color = '#FF6B6B'

lyrics = generator.parse_lrc_file('song.lrc')
generator.generate_video(
    lyrics=lyrics,
    audio_path="song.mp3",
    background_source="background.mp4",
    output_path="output.mp4",
    animation="bounce",
    show_current_and_next=False
)"""
        },
        {
            "title": "批量处理",
            "code": """
# 批量生成多个视频
songs = [
    ("song1.mp3", "lyrics1.lrc", "bg1.jpg"),
    ("song2.mp3", "lyrics2.lrc", "bg2.jpg"),
]

generator = LyricVideoGenerator()
for i, (audio, lrc, bg) in enumerate(songs):
    lyrics = generator.parse_lrc_file(lrc)
    generator.generate_video(
        lyrics=lyrics,
        audio_path=audio,
        background_source=bg,
        output_path=f"video_{i+1}.mp4"
    )"""
        }
    ]
    
    for example in examples:
        print(f"\n{example['title']}:")
        print(example['code'])


def main():
    """主函数"""
    print("高级歌词视频生成器演示")
    print("=" * 60)
    
    # 演示不同功能
    demo_different_animations()
    demo_custom_styling()
    show_usage_examples()
    
    # 尝试生成完整演示
    print("\n" + "=" * 60)
    print("准备生成演示视频...")
    
    # 检查是否要生成演示视频
    try:
        generate_complete_demo()
    except Exception as e:
        print(f"演示视频生成遇到问题: {e}")
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("\n生成的文件:")
    files = ['advanced_lyrics.lrc', 'background.jpg', 'sample_audio.wav', 'demo_lyric_video.mp4']
    for file in files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (未生成)")
    
    print("\n下一步:")
    print("1. 准备您的音频文件")
    print("2. 创建或获取LRC歌词文件")
    print("3. 选择背景图片或视频")
    print("4. 使用生成器创建您的歌词视频！")


if __name__ == "__main__":
    main()
