"""
精武英雄歌词视频生成器 - 修复版
直接集成修复功能，专门为甄子丹《精武英雄》创建歌词视频
"""

import os
import re
from typing import List, Tuple, Union
from moviepy.editor import *
from PIL import Image, ImageDraw, ImageFont
import numpy as np


class JingwuHeroGenerator:
    """精武英雄专用歌词视频生成器"""
    
    def __init__(self, width: int = 1920, height: int = 1080, fps: int = 30):
        self.width = width
        self.height = height
        self.fps = fps
        self.default_font_size = 80
        self.default_font_color = 'white'
        self.highlight_color = '#FFD700'  # 金色
        
    def parse_lrc_file(self, lrc_path: str) -> List[Tuple[float, str]]:
        """解析LRC歌词文件"""
        lyrics = []
        
        with open(lrc_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for line in lines:
            line = line.strip()
            # 匹配时间戳格式 [mm:ss.xx]
            time_match = re.match(r'\[(\d{2}):(\d{2})\.(\d{2})\](.*)', line)
            if time_match:
                minutes = int(time_match.group(1))
                seconds = int(time_match.group(2))
                centiseconds = int(time_match.group(3))
                text = time_match.group(4).strip()
                
                # 转换为总秒数
                timestamp = minutes * 60 + seconds + centiseconds / 100
                
                if text:  # 只添加非空文本
                    lyrics.append((timestamp, text))
                    
        return sorted(lyrics, key=lambda x: x[0])
    
    def create_text_clip(self, text: str, start_time: float, duration: float, 
                        is_highlighted: bool = False, position: Union[str, Tuple] = 'center',
                        font_size: int = None, animation: str = 'none') -> TextClip:
        """创建文本片段"""
        if font_size is None:
            font_size = self.default_font_size
            
        color = self.highlight_color if is_highlighted else self.default_font_color
        
        # 创建文本片段
        txt_clip = TextClip(text, 
                           fontsize=font_size,
                           color=color,
                           font='Arial-Bold',
                           stroke_color='black',
                           stroke_width=2)
        
        # 设置位置
        txt_clip = txt_clip.set_position(position)
        
        # 应用动画效果
        if animation == 'fade':
            txt_clip = txt_clip.crossfadein(0.3).crossfadeout(0.3)
        
        # 设置时间
        txt_clip = txt_clip.set_start(start_time).set_duration(duration)
        
        return txt_clip
    
    def create_background(self, background_source: str, duration: float) -> VideoClip:
        """创建背景"""
        if background_source.startswith('#') or background_source.lower() in ['black', 'white', 'blue', 'red', 'green']:
            # 纯色背景
            return ColorClip(size=(self.width, self.height), 
                           color=background_source, 
                           duration=duration)
        elif os.path.exists(background_source):
            try:
                # 先用PIL加载图片，然后转换
                img = Image.open(background_source)
                img = img.resize((self.width, self.height), Image.Resampling.LANCZOS)
                
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                img_array = np.array(img)
                return ImageClip(img_array, duration=duration)
                
            except Exception as e:
                print(f"背景加载失败，使用黑色背景: {e}")
                return ColorClip(size=(self.width, self.height), 
                               color='black', 
                               duration=duration)
        else:
            # 默认黑色背景
            return ColorClip(size=(self.width, self.height), 
                           color='black', 
                           duration=duration)
    
    def generate_video(self, lyrics: List[Tuple[float, str]], 
                      audio_path: str,
                      background_source: str = 'black',
                      output_path: str = 'lyric_video.mp4',
                      animation: str = 'fade',
                      show_current_and_next: bool = True) -> None:
        """生成歌词视频"""
        print(f"🎬 开始生成歌词视频: {output_path}")
        
        # 加载音频
        print("📻 加载音频文件...")
        audio = AudioFileClip(audio_path)
        duration = audio.duration
        print(f"   音频时长: {duration:.1f} 秒")
        
        # 创建背景
        print("🖼️  创建背景...")
        background = self.create_background(background_source, duration)
        
        # 创建文本片段列表
        print("📝 创建歌词文本...")
        text_clips = []
        
        for i, (start_time, text) in enumerate(lyrics):
            # 计算当前歌词的结束时间
            if i < len(lyrics) - 1:
                end_time = lyrics[i + 1][0]
            else:
                end_time = duration
            
            lyric_duration = end_time - start_time
            
            if show_current_and_next:
                # 当前歌词（高亮）
                current_clip = self.create_text_clip(
                    text, start_time, lyric_duration,
                    is_highlighted=True,
                    position=('center', self.height/2 - 60),
                    animation=animation
                )
                text_clips.append(current_clip)
                
                # 下一句歌词（普通显示）
                if i < len(lyrics) - 1:
                    next_text = lyrics[i + 1][1]
                    next_clip = self.create_text_clip(
                        next_text, start_time, lyric_duration,
                        is_highlighted=False,
                        position=('center', self.height/2 + 60),
                        font_size=self.default_font_size - 20
                    )
                    text_clips.append(next_clip)
            else:
                # 只显示当前歌词
                current_clip = self.create_text_clip(
                    text, start_time, lyric_duration,
                    is_highlighted=True,
                    position='center',
                    animation=animation
                )
                text_clips.append(current_clip)
        
        print(f"   创建了 {len(text_clips)} 个文本片段")
        
        # 合成最终视频
        print("🎞️  合成视频...")
        final_video = CompositeVideoClip([background] + text_clips)
        final_video = final_video.set_audio(audio)
        final_video = final_video.set_fps(self.fps)
        
        # 输出视频
        print(f"💾 导出视频: {output_path}")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )
        
        print("✅ 歌词视频生成完成！")


def create_martial_arts_background():
    """创建武术风格的背景图片"""
    print("🥋 创建精武英雄主题背景...")
    
    width, height = 1920, 1080
    image = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(image)
    
    # 创建深色渐变背景（深红到黑色）
    for y in range(height):
        ratio = y / height
        # 深红(139, 0, 0) 到 黑色(0, 0, 0)
        r = int(139 * (1 - ratio))
        g = int(0)
        b = int(0)
        
        color = (r, g, b)
        draw.line([(0, y), (width, y)], fill=color)
    
    # 添加一些装饰性的几何图案
    for i in range(15):
        x = np.random.randint(100, width-100)
        y = np.random.randint(100, height-100)
        radius = np.random.randint(30, 120)
        
        # 半透明的金色圆圈
        alpha = np.random.randint(20, 60)
        light_color = (int(255*alpha/255), int(215*alpha/255), int(0*alpha/255))
        draw.ellipse([x-radius, y-radius, x+radius, y+radius], 
                    outline=light_color, width=2)
    
    # 添加标题区域
    try:
        title_font = ImageFont.truetype("simsun.ttc", 72)  # 宋体
        subtitle_font = ImageFont.truetype("arial.ttf", 48)
    except:
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
    
    # 添加标题文字
    title = "精武英雄"
    subtitle = "Jingwu Hero"
    
    # 计算文字位置（居中）
    title_bbox = draw.textbbox((0, 0), title, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (width - title_width) // 2
    title_y = 100
    
    subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    subtitle_x = (width - subtitle_width) // 2
    subtitle_y = title_y + 100
    
    # 添加文字阴影
    shadow_offset = 3
    draw.text((title_x + shadow_offset, title_y + shadow_offset), title, 
              fill=(0, 0, 0), font=title_font)
    draw.text((subtitle_x + shadow_offset, subtitle_y + shadow_offset), subtitle, 
              fill=(0, 0, 0), font=subtitle_font)
    
    # 添加主文字（金色）
    draw.text((title_x, title_y), title, fill=(255, 215, 0), font=title_font)
    draw.text((subtitle_x, subtitle_y), subtitle, fill=(255, 215, 0), font=subtitle_font)
    
    image.save('jingwu_hero_background.jpg', 'JPEG', quality=95)
    print("✅ 精武英雄背景已创建: jingwu_hero_background.jpg")


def generate_jingwu_hero_video():
    """生成精武英雄歌词视频"""
    print("🥋 精武英雄歌词视频生成器")
    print("=" * 60)
    
    # 检查必要文件
    audio_file = "精武英雄 - 甄子丹.flac"
    lyrics_file = "精武英雄 - 甄子丹.lrc"
    
    print("📁 检查文件:")
    files_ok = True
    if os.path.exists(audio_file):
        print(f"   ✅ {audio_file}")
    else:
        print(f"   ❌ {audio_file}")
        files_ok = False
        
    if os.path.exists(lyrics_file):
        print(f"   ✅ {lyrics_file}")
    else:
        print(f"   ❌ {lyrics_file}")
        files_ok = False
    
    if not files_ok:
        print("❌ 缺少必要文件，无法生成视频")
        return False
    
    # 创建背景
    create_martial_arts_background()
    
    # 创建生成器
    generator = JingwuHeroGenerator(width=1920, height=1080, fps=30)
    generator.default_font_size = 72
    generator.highlight_color = '#FFD700'  # 金色高亮
    
    # 解析歌词
    print("\n📖 解析歌词...")
    lyrics = generator.parse_lrc_file(lyrics_file)
    
    print(f"找到歌词行数: {len(lyrics)}")
    for i, (timestamp, text) in enumerate(lyrics[:8]):  # 显示前8行
        print(f"  {timestamp:6.1f}s: {text}")
    if len(lyrics) > 8:
        print(f"  ... 还有 {len(lyrics) - 8} 行歌词")
    
    try:
        print(f"\n🎬 开始生成视频...")
        generator.generate_video(
            lyrics=lyrics,
            audio_path=audio_file,
            background_source='jingwu_hero_background.jpg',
            output_path='精武英雄_歌词视频.mp4',
            animation='fade',
            show_current_and_next=True
        )
        
        print("\n🎉 视频生成成功!")
        print("📁 输出文件: 精武英雄_歌词视频.mp4")
        return True
        
    except Exception as e:
        print(f"\n❌ 视频生成失败: {e}")
        print("可能的解决方案:")
        print("1. 检查音频文件格式是否支持")
        print("2. 确保有足够的磁盘空间")
        print("3. 检查是否安装了所有依赖库")
        return False


def generate_test_version():
    """生成测试版本（前30秒）"""
    print("🧪 生成测试版本（前30秒）")
    print("=" * 40)
    
    audio_file = "精武英雄 - 甄子丹.flac"
    lyrics_file = "精武英雄 - 甄子丹.lrc"
    
    if not os.path.exists(audio_file) or not os.path.exists(lyrics_file):
        print("❌ 缺少必要文件")
        return False
    
    # 创建背景
    create_martial_arts_background()
    
    # 创建生成器
    generator = JingwuHeroGenerator(width=1280, height=720, fps=24)
    generator.default_font_size = 60
    
    # 解析歌词并只取前30秒
    all_lyrics = generator.parse_lrc_file(lyrics_file)
    test_lyrics = [(t, text) for t, text in all_lyrics if t <= 30.0]
    
    print(f"测试歌词（前30秒，共{len(test_lyrics)}行）:")
    for timestamp, text in test_lyrics:
        print(f"  {timestamp:6.1f}s: {text}")
    
    if not test_lyrics:
        print("❌ 前30秒没有找到歌词")
        return False
    
    try:
        generator.generate_video(
            lyrics=test_lyrics,
            audio_path=audio_file,
            background_source='jingwu_hero_background.jpg',
            output_path='精武英雄_测试版.mp4',
            animation='fade',
            show_current_and_next=False
        )
        
        print("✅ 测试版视频生成成功: 精武英雄_测试版.mp4")
        print("💡 注意: 包含完整音频，可手动剪辑前30秒")
        return True
        
    except Exception as e:
        print(f"❌ 测试版生成失败: {e}")
        return False


def main():
    """主函数"""
    print("🥋 精武英雄歌词视频生成器")
    print("=" * 60)
    
    print("📋 选择生成模式:")
    print("1. 测试版 - 快速验证（推荐）")
    print("2. 完整版 - 生成完整视频")
    
    # 自动运行测试版
    print("\n🚀 自动运行测试版...")
    test_success = generate_test_version()
    
    if test_success:
        print("\n" + "=" * 60)
        print("🎉 测试成功！")
        print("\n若要生成完整版，请运行:")
        print("python -c \"from jingwu_hero_final import generate_jingwu_hero_video; generate_jingwu_hero_video()\"")
    else:
        print("\n❌ 测试失败，请检查文件和环境")
    
    print("\n📝 技术规格:")
    print("- 测试版: 1280x720, 24fps")
    print("- 完整版: 1920x1080, 30fps")
    print("- 字体: 金色高亮，武术主题背景")
    print("- 格式: MP4 (H.264 + AAC)")


if __name__ == "__main__":
    main()
