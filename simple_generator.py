"""
精武英雄歌词视频生成器 - 简化版
不依赖ImageMagick，使用纯Python实现
"""

import os
import re
from typing import List, Tuple, Union
from moviepy.editor import *
from PIL import Image, ImageDraw, ImageFont
import numpy as np


class SimpleJingwuGenerator:
    """简化版精武英雄歌词视频生成器"""
    
    def __init__(self, width: int = 1920, height: int = 1080, fps: int = 30):
        self.width = width
        self.height = height
        self.fps = fps
        self.default_font_size = 80
        self.default_font_color = 'white'
        self.highlight_color = '#FFD700'  # 金色
        
    def parse_lrc_file(self, lrc_path: str) -> List[Tuple[float, str]]:
        """解析LRC歌词文件"""
        lyrics = []
        
        with open(lrc_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for line in lines:
            line = line.strip()
            time_match = re.match(r'\[(\d{2}):(\d{2})\.(\d{2})\](.*)', line)
            if time_match:
                minutes = int(time_match.group(1))
                seconds = int(time_match.group(2))
                centiseconds = int(time_match.group(3))
                text = time_match.group(4).strip()
                
                timestamp = minutes * 60 + seconds + centiseconds / 100
                
                if text:
                    lyrics.append((timestamp, text))
                    
        return sorted(lyrics, key=lambda x: x[0])
    
    def create_text_image(self, text: str, font_size: int, color: str, 
                         width: int, height: int, y_position: int) -> np.ndarray:
        """使用PIL创建文字图像"""
        # 创建透明背景图像
        img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 尝试加载字体
        try:
            if '中' in text or '英' in text or '武' in text:  # 包含中文
                font = ImageFont.truetype("simsun.ttc", font_size)
            else:
                font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()
        
        # 计算文字位置（居中）
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (width - text_width) // 2
        y = y_position - text_height // 2
        
        # 绘制文字阴影
        shadow_color = (0, 0, 0, 180)
        draw.text((x + 2, y + 2), text, fill=shadow_color, font=font)
        
        # 绘制主文字
        if color == '#FFD700':  # 金色
            main_color = (255, 215, 0, 255)
        else:  # 白色
            main_color = (255, 255, 255, 255)
            
        draw.text((x, y), text, fill=main_color, font=font)
        
        # 转换为RGB numpy数组
        img_rgb = Image.new('RGB', (width, height), (0, 0, 0))
        img_rgb.paste(img, (0, 0), img)
        
        return np.array(img_rgb)
    
    def create_lyric_clip(self, text: str, start_time: float, duration: float,
                         is_highlighted: bool = False, y_position: int = None) -> ImageClip:
        """创建歌词片段"""
        if y_position is None:
            y_position = self.height // 2
            
        font_size = self.default_font_size if is_highlighted else self.default_font_size - 20
        color = self.highlight_color if is_highlighted else self.default_font_color
        
        # 创建文字图像
        text_img = self.create_text_image(text, font_size, color, 
                                        self.width, self.height, y_position)
        
        # 创建ImageClip
        clip = ImageClip(text_img, duration=duration)
        clip = clip.set_start(start_time)
        
        # 添加淡入淡出效果
        if duration > 0.6:
            clip = clip.crossfadein(0.3).crossfadeout(0.3)
        
        return clip
    
    def create_background_clip(self, duration: float) -> ColorClip:
        """创建纯色背景"""
        return ColorClip(size=(self.width, self.height), 
                        color=(139, 0, 0),  # 深红色
                        duration=duration)
    
    def generate_simple_video(self, lyrics: List[Tuple[float, str]], 
                            audio_path: str, output_path: str) -> bool:
        """生成简化版歌词视频"""
        try:
            print(f"🎬 开始生成: {output_path}")
            
            # 加载音频
            print("📻 加载音频...")
            audio = AudioFileClip(audio_path)
            duration = audio.duration
            print(f"   音频时长: {duration:.1f} 秒")
            
            # 创建背景
            print("🎨 创建背景...")
            background = self.create_background_clip(duration)
            
            # 创建歌词片段
            print("📝 创建歌词片段...")
            clips = [background]
            
            for i, (start_time, text) in enumerate(lyrics):
                # 计算结束时间
                if i < len(lyrics) - 1:
                    end_time = lyrics[i + 1][0]
                else:
                    end_time = duration
                
                lyric_duration = end_time - start_time
                
                # 创建当前歌词（高亮）
                current_clip = self.create_lyric_clip(
                    text, start_time, lyric_duration,
                    is_highlighted=True,
                    y_position=self.height // 2 - 50
                )
                clips.append(current_clip)
                
                # 创建下一句歌词（预览）
                if i < len(lyrics) - 1:
                    next_text = lyrics[i + 1][1]
                    next_clip = self.create_lyric_clip(
                        next_text, start_time, lyric_duration,
                        is_highlighted=False,
                        y_position=self.height // 2 + 50
                    )
                    clips.append(next_clip)
            
            print(f"   创建了 {len(clips)} 个片段")
            
            # 合成视频
            print("🎞️  合成视频...")
            final_video = CompositeVideoClip(clips)
            final_video = final_video.set_audio(audio)
            final_video = final_video.set_fps(self.fps)
            
            # 导出视频
            print("💾 导出视频...")
            final_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )
            
            print("✅ 视频生成成功！")
            return True
            
        except Exception as e:
            print(f"❌ 生成失败: {e}")
            return False


def quick_test():
    """快速测试"""
    print("🧪 快速测试 - 精武英雄歌词视频")
    print("=" * 50)
    
    # 检查文件
    audio_file = "精武英雄 - 甄子丹.flac"
    lyrics_file = "精武英雄 - 甄子丹.lrc"
    
    if not os.path.exists(audio_file):
        print(f"❌ 音频文件不存在: {audio_file}")
        return False
        
    if not os.path.exists(lyrics_file):
        print(f"❌ 歌词文件不存在: {lyrics_file}")
        return False
    
    print("✅ 文件检查通过")
    
    # 创建生成器
    generator = SimpleJingwuGenerator(width=1280, height=720, fps=24)
    generator.default_font_size = 60
    
    # 解析歌词 - 只用前几句测试
    all_lyrics = generator.parse_lrc_file(lyrics_file)
    test_lyrics = [(t, text) for t, text in all_lyrics if t <= 35.0]  # 前35秒
    
    print(f"\n📖 测试歌词（前35秒，共{len(test_lyrics)}行）:")
    for timestamp, text in test_lyrics:
        print(f"  {timestamp:6.1f}s: {text}")
    
    if not test_lyrics:
        print("❌ 没有找到测试歌词")
        return False
    
    # 生成视频
    success = generator.generate_simple_video(
        lyrics=test_lyrics,
        audio_path=audio_file,
        output_path="精武英雄_简化测试.mp4"
    )
    
    if success:
        print("\n🎉 测试成功！")
        print("📁 输出文件: 精武英雄_简化测试.mp4")
        print("\n💡 如果测试效果满意，可以生成完整版本")
    
    return success


def generate_full_version():
    """生成完整版本"""
    print("🎬 生成完整版 - 精武英雄歌词视频")
    print("=" * 50)
    
    audio_file = "精武英雄 - 甄子丹.flac"
    lyrics_file = "精武英雄 - 甄子丹.lrc"
    
    # 创建高质量生成器
    generator = SimpleJingwuGenerator(width=1920, height=1080, fps=30)
    generator.default_font_size = 80
    
    # 解析完整歌词
    lyrics = generator.parse_lrc_file(lyrics_file)
    print(f"📖 完整歌词: {len(lyrics)} 行")
    
    # 生成完整视频
    success = generator.generate_simple_video(
        lyrics=lyrics,
        audio_path=audio_file,
        output_path="精武英雄_完整版.mp4"
    )
    
    if success:
        print("\n🏆 完整版生成成功！")
        print("📁 输出文件: 精武英雄_完整版.mp4")
    
    return success


def main():
    """主函数"""
    print("🥋 精武英雄歌词视频生成器 - 简化版")
    print("=" * 60)
    print("🔧 特点: 不依赖ImageMagick，纯Python实现")
    print()
    
    # 先运行快速测试
    test_success = quick_test()
    
    if test_success:
        print("\n" + "=" * 60)
        print("🚀 测试成功！现在可以生成完整版本")
        print("\n运行完整版生成:")
        print("python -c \"from simple_jingwu_generator import generate_full_version; generate_full_version()\"")
    else:
        print("\n❌ 测试失败，请检查：")
        print("1. 音频文件是否存在")
        print("2. 歌词文件是否存在")
        print("3. Python环境是否正确")
    
    print("\n📋 项目文件:")
    files = [
        "精武英雄 - 甄子丹.flac",
        "精武英雄 - 甄子丹.lrc",
        "精武英雄_简化测试.mp4",
        "精武英雄_完整版.mp4"
    ]
    
    for file in files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ⭕ {file}")


if __name__ == "__main__":
    main()
