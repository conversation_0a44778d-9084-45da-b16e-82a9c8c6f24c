"""
简化的歌词视频生成器示例
不依赖额外的库，直接展示核心功能
"""

from lyric_video_generator import LyricVideoGenerator
import os
from PIL import Image, ImageDraw


def create_simple_background():
    """创建一个简单的背景图片"""
    print("创建示例背景图片...")
    
    # 创建渐变背景
    width, height = 1280, 720
    image = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(image)
    
    # 创建从深蓝到浅蓝的垂直渐变
    for y in range(height):
        ratio = y / height
        # 深蓝(25, 25, 112) 到 浅蓝(135, 206, 235)
        r = int(25 + (135 - 25) * ratio)
        g = int(25 + (206 - 25) * ratio)  
        b = int(112 + (235 - 112) * ratio)
        
        color = (r, g, b)
        draw.line([(0, y), (width, y)], fill=color)
    
    # 添加标题文字
    try:
        from PIL import ImageFont
        font = ImageFont.truetype("arial.ttf", 40)
    except:
        font = ImageFont.load_default()
    
    # 添加标题
    title = "歌词视频演示背景"
    bbox = draw.textbbox((0, 0), title, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (width - text_width) // 2
    y = 50
    
    # 添加阴影
    draw.text((x+2, y+2), title, fill=(0, 0, 0, 128), font=font)
    # 添加主文字
    draw.text((x, y), title, fill=(255, 255, 255), font=font)
    
    image.save('demo_background.jpg', 'JPEG')
    print("✅ 背景图片已创建: demo_background.jpg")


def create_sample_lrc():
    """创建示例LRC歌词文件"""
    print("创建示例歌词文件...")
    
    lrc_content = """[00:00.00]歌词视频生成器
[00:03.00]功能演示开始
[00:06.00]支持LRC格式解析
[00:09.00]多种动画效果
[00:12.00]自定义背景和字体
[00:15.00]专业级视频输出
[00:18.00]演示即将结束
[00:21.00]感谢使用本工具
"""
    
    with open('demo_lyrics.lrc', 'w', encoding='utf-8') as f:
        f.write(lrc_content)
    
    print("✅ 歌词文件已创建: demo_lyrics.lrc")


def show_generator_features():
    """展示生成器的主要功能"""
    print("\n=== 歌词视频生成器主要功能 ===")
    
    # 创建生成器实例
    generator = LyricVideoGenerator(width=1280, height=720, fps=24)
    
    print("📹 视频设置:")
    print(f"   分辨率: {generator.width} x {generator.height}")
    print(f"   帧率: {generator.fps} fps")
    
    print("\n🎨 文字样式:")
    print(f"   默认字体大小: {generator.default_font_size}")
    print(f"   默认文字颜色: {generator.default_font_color}")
    print(f"   高亮颜色: {generator.highlight_color}")
    
    print("\n✨ 支持的动画:")
    animations = ['none', 'fade', 'slide', 'bounce']
    for anim in animations:
        print(f"   - {anim}")
    
    print("\n🖼️ 背景类型:")
    backgrounds = [
        "纯色背景: 'black', 'white', '#FF0000'",
        "图片背景: 'image.jpg', 'image.png'", 
        "视频背景: 'video.mp4', 'video.avi'"
    ]
    for bg in backgrounds:
        print(f"   - {bg}")
    
    print("\n📝 歌词格式:")
    print("   - LRC文件: [mm:ss.xx]歌词文本")
    print("   - 手动输入: [(时间戳, 文本), ...]")


def demonstrate_lrc_parsing():
    """演示LRC文件解析功能"""
    print("\n=== LRC文件解析演示 ===")
    
    # 创建示例文件
    create_sample_lrc()
    
    # 创建生成器并解析
    generator = LyricVideoGenerator()
    lyrics = generator.parse_lrc_file('demo_lyrics.lrc')
    
    print("解析结果:")
    for timestamp, text in lyrics:
        minutes = int(timestamp // 60)
        seconds = timestamp % 60
        print(f"   {minutes:02d}:{seconds:05.2f} - {text}")
    
    return lyrics


def show_manual_lyrics_example():
    """展示手动歌词输入示例"""
    print("\n=== 手动歌词输入示例 ===")
    
    lyrics = [
        (0.0, "这是手动输入的歌词"),
        (2.5, "可以精确控制时间"),
        (5.0, "支持任意时间戳"),
        (7.5, "不需要LRC文件"),
        (10.0, "编程方式更灵活")
    ]
    
    print("手动歌词数据:")
    for timestamp, text in lyrics:
        print(f"   {timestamp:5.1f}s: {text}")
    
    return lyrics


def show_complete_usage():
    """展示完整的使用方法"""
    print("\n=== 完整使用示例 ===")
    
    create_simple_background()
    lyrics = demonstrate_lrc_parsing()
    
    print("\n📋 生成视频的完整步骤:")
    print("""
1. 创建生成器实例:
   generator = LyricVideoGenerator(width=1280, height=720, fps=24)

2. 准备歌词数据:
   # 方法1: 解析LRC文件
   lyrics = generator.parse_lrc_file('demo_lyrics.lrc')
   
   # 方法2: 手动输入
   lyrics = [(0.0, "第一句"), (3.0, "第二句"), ...]

3. 生成视频:
   generator.generate_video(
       lyrics=lyrics,
       audio_path="your_audio.mp3",      # 您的音频文件
       background_source="demo_background.jpg",  # 背景
       output_path="lyric_video.mp4",    # 输出文件
       animation="fade",                 # 动画效果
       show_current_and_next=True        # 显示模式
   )
""")
    
    print("⚠️  注意事项:")
    print("   - 需要提供音频文件才能生成完整视频")
    print("   - 支持的音频格式: .mp3, .wav, .m4a, .aac")
    print("   - 背景可以是图片、视频或纯色")
    print("   - 生成过程可能需要几分钟时间")


def create_quick_test():
    """创建快速测试函数"""
    print("\n=== 快速测试代码 ===")
    
    test_code = '''
# 快速测试代码（需要音频文件）
from lyric_video_generator import LyricVideoGenerator

def quick_test():
    """快速测试歌词视频生成"""
    # 创建生成器
    generator = LyricVideoGenerator(width=1280, height=720)
    
    # 准备测试歌词
    lyrics = [
        (0.0, "快速测试开始"),
        (3.0, "第一句测试歌词"),
        (6.0, "第二句测试歌词"), 
        (9.0, "测试即将结束"),
        (12.0, "感谢测试使用")
    ]
    
    # 生成视频（需要替换为实际音频文件）
    generator.generate_video(
        lyrics=lyrics,
        audio_path="test_audio.mp3",  # 替换为您的音频文件
        background_source="black",    # 简单黑色背景
        output_path="test_video.mp4",
        animation="fade"
    )
    
    print("测试视频生成完成!")

# 运行测试
# quick_test()
'''
    
    print(test_code)
    
    # 保存测试代码到文件
    with open('quick_test.py', 'w', encoding='utf-8') as f:
        f.write(test_code)
    print("✅ 快速测试代码已保存到: quick_test.py")


def main():
    """主函数"""
    print("🎵 歌词视频生成器 - 完整功能演示")
    print("=" * 60)
    
    # 展示各种功能
    show_generator_features()
    demonstrate_lrc_parsing()
    show_manual_lyrics_example()
    show_complete_usage()
    create_quick_test()
    
    print("\n" + "=" * 60)
    print("📁 生成的文件:")
    files = ['demo_background.jpg', 'demo_lyrics.lrc', 'quick_test.py']
    for file in files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file}")
    
    print("\n🚀 下一步操作:")
    print("1. 准备一个音频文件 (如 song.mp3)")
    print("2. 编辑 quick_test.py 中的音频文件路径")
    print("3. 运行: python quick_test.py")
    print("4. 等待视频生成完成！")
    
    print("\n💡 提示:")
    print("- 第一次生成可能需要下载ffmpeg")
    print("- 视频质量和生成速度可以在代码中调整")
    print("- 支持中文歌词和各种字体")


if __name__ == "__main__":
    main()
