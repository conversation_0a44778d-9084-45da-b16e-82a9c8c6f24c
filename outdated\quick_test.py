
# 快速测试代码（需要音频文件）
from lyric_video_generator import LyricVideoGenerator

def quick_test():
    """快速测试歌词视频生成"""
    # 创建生成器
    generator = LyricVideoGenerator(width=1280, height=720)
    
    # 准备测试歌词
    lyrics = [
        (0.0, "快速测试开始"),
        (3.0, "第一句测试歌词"),
        (6.0, "第二句测试歌词"), 
        (9.0, "测试即将结束"),
        (12.0, "感谢测试使用")
    ]
    
    # 生成视频（需要替换为实际音频文件）
    generator.generate_video(
        lyrics=lyrics,
        audio_path="test_audio.mp3",  # 替换为您的音频文件
        background_source="black",    # 简单黑色背景
        output_path="test_video.mp4",
        animation="fade"
    )
    
    print("测试视频生成完成!")

# 运行测试
# quick_test()
