"""
歌词视频生成器测试脚本
演示如何使用LyricVideoGenerator类
"""

from lyric_video_generator import LyricVideoGenerator
import os


def create_sample_lrc_file():
    """创建示例LRC歌词文件"""
    lrc_content = """[00:00.00]歌词视频生成器演示
[00:03.00]这是第一句歌词
[00:06.00]支持LRC格式解析
[00:09.00]可以自定义动画效果
[00:12.00]背景支持图片和视频
[00:15.00]文字同步显示功能
[00:18.00]感谢使用本工具
"""
    
    with open('sample_lyrics.lrc', 'w', encoding='utf-8') as f:
        f.write(lrc_content)
    
    print("示例LRC文件已创建: sample_lyrics.lrc")


def test_manual_lyrics():
    """测试手动歌词输入"""
    print("\n=== 测试手动歌词输入 ===")
    
    # 创建生成器实例
    generator = LyricVideoGenerator(width=1280, height=720, fps=24)
    
    # 手动创建歌词数据
    lyrics = [
        (0.0, "手动歌词测试"),
        (2.5, "第一句歌词内容"),
        (5.0, "第二句歌词内容"),
        (7.5, "支持多种动画"),
        (10.0, "可自定义样式"),
        (12.5, "测试完成")
    ]
    
    print("歌词数据:")
    for timestamp, text in lyrics:
        print(f"  {timestamp:6.1f}s: {text}")
    
    return lyrics


def test_lrc_parsing():
    """测试LRC文件解析"""
    print("\n=== 测试LRC文件解析 ===")
    
    # 创建示例LRC文件
    create_sample_lrc_file()
    
    # 创建生成器实例
    generator = LyricVideoGenerator()
    
    # 解析LRC文件
    if os.path.exists('sample_lyrics.lrc'):
        lyrics = generator.parse_lrc_file('sample_lyrics.lrc')
        print("解析的歌词:")
        for timestamp, text in lyrics:
            print(f"  {timestamp:6.1f}s: {text}")
        return lyrics
    else:
        print("LRC文件不存在")
        return []


def demonstrate_features():
    """演示生成器功能"""
    print("\n=== 歌词视频生成器功能演示 ===")
    print("1. 支持的动画效果:")
    print("   - none: 无动画")
    print("   - fade: 淡入淡出")
    print("   - slide: 滑动进入")
    print("   - bounce: 弹跳效果")
    
    print("\n2. 支持的背景类型:")
    print("   - 纯色: 'black', 'white', 'blue', '#FF0000'")
    print("   - 图片: 'background.jpg', 'background.png'")
    print("   - 视频: 'background.mp4', 'background.avi'")
    
    print("\n3. 歌词显示模式:")
    print("   - 单行模式: 只显示当前歌词")
    print("   - 双行模式: 显示当前歌词(高亮) + 下一句歌词")
    
    print("\n4. 自定义选项:")
    print("   - 视频尺寸: 1920x1080, 1280x720, 自定义")
    print("   - 帧率: 24fps, 30fps, 60fps")
    print("   - 字体大小和颜色")
    print("   - 高亮颜色和效果")


def create_demo_without_audio():
    """创建无音频的演示视频（仅供测试文字效果）"""
    print("\n=== 创建演示视频（无音频） ===")
    
    generator = LyricVideoGenerator(width=1280, height=720, fps=24)
    
    # 使用测试歌词
    lyrics = test_manual_lyrics()
    
    print("\n注意: 由于没有音频文件，无法生成完整视频")
    print("如果您有音频文件，可以使用以下代码:")
    print("""
# 示例代码
generator.generate_video(
    lyrics=lyrics,
    audio_path="your_song.mp3",  # 替换为您的音频文件
    background_source="black",   # 或使用图片/视频背景
    output_path="lyric_video.mp4",
    animation="fade",
    show_current_and_next=True
)
""")


def main():
    """主函数"""
    print("歌词视频生成器测试程序")
    print("=" * 60)
    
    # 演示功能
    demonstrate_features()
    
    # 测试手动歌词
    manual_lyrics = test_manual_lyrics()
    
    # 测试LRC解析
    lrc_lyrics = test_lrc_parsing()
    
    # 演示如何生成视频
    create_demo_without_audio()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n使用说明:")
    print("1. 准备音频文件 (.mp3, .wav, .m4a 等)")
    print("2. 准备歌词文件 (.lrc) 或手动输入歌词时间戳")
    print("3. 可选：准备背景图片或视频")
    print("4. 调用 generator.generate_video() 方法生成视频")
    
    print("\n示例完整用法:")
    print("""
from lyric_video_generator import LyricVideoGenerator

# 创建生成器
generator = LyricVideoGenerator(width=1920, height=1080)

# 解析LRC文件或手动创建歌词
lyrics = generator.parse_lrc_file('song.lrc')
# 或者
lyrics = [(0.0, "第一句"), (3.0, "第二句"), ...]

# 生成视频
generator.generate_video(
    lyrics=lyrics,
    audio_path="song.mp3",
    background_source="background.jpg",
    output_path="output.mp4",
    animation="fade"
)
""")


if __name__ == "__main__":
    main()
