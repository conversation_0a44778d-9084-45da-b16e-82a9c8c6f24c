"""
精武英雄歌词视频生成器 - 增强版（重构修复终版）
支持背景图片、发光效果和双语模式
"""

import os
import re
from typing import List, Tuple, Optional
from moviepy.editor import AudioFileClip, ImageClip, CompositeVideoClip, ColorClip
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
import numpy as np
import traceback
from pathlib import Path
from lrc_mv_config import LrcMvConfig, load_lrc_mv_config

DEFAULT_WIDTH = 720
DEFAULT_HEIGHT = 1280
DEFAULT_FPS = 24

class EnhancedJingwuGenerator:
    """增强版精武英雄歌词视频生成器（重构修复终版）"""
    
    def __init__(self, width: int = DEFAULT_WIDTH, height: int = DEFAULT_HEIGHT, fps: int = DEFAULT_FPS):
        self.width = width
        self.height = height
        self.fps = fps
        self.default_font_size = 80
        self.default_font_color = 'white'
        self.highlight_color = '#FFD700'  # 金色
        self.shadow_color = (0, 0, 0, 200)
        
        self.theme_colors = {
            'gold': '#FFD700',
            'red': '#DC143C',
            'dark_red': '#8B0000',
            'black': '#000000',
            'white': '#FFFFFF',
            'silver': '#C0C0C0'
        }
        
    def parse_lrc_file(self, lrc_path: str) -> List[Tuple[float, str]]:
        """解析LRC歌词文件"""
        lyrics = []
        with open(lrc_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        for line in lines:
            line = line.strip()
            time_match = re.match(r'\[(\d{2}):(\d{2})\.(\d{2})\](.*)', line)
            if time_match:
                minutes = int(time_match.group(1))
                seconds = int(time_match.group(2))
                centiseconds = int(time_match.group(3))
                text = time_match.group(4).strip()
                timestamp = minutes * 60 + seconds + centiseconds / 100
                if text:
                    lyrics.append((timestamp, text))
        return sorted(lyrics, key=lambda x: x[0])
    
    def load_background_image(self, bg_path: str) -> Optional[np.ndarray]:
        """加载并处理背景图片"""
        try:
            img = Image.open(bg_path)
            # PIL版本兼容性处理
            try:
                img = img.resize((self.width, self.height), Image.Resampling.LANCZOS)
            except AttributeError:
                # 较旧的PIL版本回退
                img = img.resize((self.width, self.height))
            
            enhancer = ImageEnhance.Brightness(img)
            img = enhancer.enhance(0.4)
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(0.6)
            img = img.filter(ImageFilter.GaussianBlur(radius=1))
            return np.array(img)
        except Exception as e:
            print(f"⚠️  背景图片加载失败: {e}")
            return None
    
    def create_gradient_background(self, color1: tuple, color2: tuple) -> np.ndarray:
        """创建渐变背景"""
        gradient = np.zeros((self.height, self.width, 3), dtype=np.uint8)
        for y in range(self.height):
            ratio = y / self.height
            r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
            g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
            b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
            gradient[y, :] = [r, g, b]
        return gradient
    
    def create_enhanced_text_image(self, text: str, font_size: int, color: str, 
                                 width: int, height: int, y_position: int,
                                 glow: bool = False) -> np.ndarray:
        """创建增强文字图像，支持发光效果"""
        scale = 2
        scaled_width = width * scale
        scaled_height = height * scale
        scaled_font_size = font_size * scale
        
        img = Image.new('RGBA', (scaled_width, scaled_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        try:
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
            if has_chinese:
                font = ImageFont.truetype("simsun.ttc", scaled_font_size)
            else:
                try:
                    font = ImageFont.truetype("arial.ttf", scaled_font_size)
                except OSError:
                    try:
                        font = ImageFont.truetype("calibri.ttf", scaled_font_size)
                    except OSError:
                        font = ImageFont.load_default()
        except OSError:
            font = ImageFont.load_default()
        
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        x = (scaled_width - text_width) // 2
        y = y_position * scale - text_height // 2
        
        if glow and color == '#FFD700':
            for offset in range(8, 0, -1):
                for dx in range(-offset, offset + 1):
                    for dy in range(-offset, offset + 1):
                        if dx * dx + dy * dy <= offset * offset:
                            alpha = max(0, 120 - offset * 15)
                            glow_rgba = (255, 215, 0, alpha)
                            draw.text((x + dx, y + dy), text, fill=glow_rgba, font=font)
        
        shadow_color_val = (0, 0, 0, 200)
        draw.text((x + 3 * scale, y + 3 * scale), text, fill=shadow_color_val, font=font)
        
        main_color = (255, 215, 0, 255) if color == '#FFD700' else (255, 255, 255, 255)
        draw.text((x, y), text, fill=main_color, font=font)
        
        # PIL版本兼容性处理
        try:
            img = img.resize((width, height), Image.Resampling.LANCZOS)
        except AttributeError:
            # 较旧的PIL版本回退
            img = img.resize((width, height))
        
        return np.array(img)

    def create_lyric_clip_with_animation(self, text: str, start_time: float, duration: float,
                                       is_highlighted: bool = False, y_position: Optional[int] = None,
                                       animation: str = 'fade') -> ImageClip:
        """创建带动画效果的歌词片段"""
        if y_position is None:
            y_position = self.height // 2
            
        font_size = self.default_font_size if is_highlighted else self.default_font_size - 20
        color = self.highlight_color if is_highlighted else self.default_font_color
        
        text_img_array = self.create_enhanced_text_image(
            text, font_size, color, self.width, self.height, y_position,
            glow=is_highlighted
        )
        
        clip = ImageClip(text_img_array, duration=duration)
        clip = clip.set_start(start_time)
          # 简化动画效果以避免渲染问题
        if animation == 'fade':
            if duration > 0.6:
                clip = clip.crossfadein(0.3).crossfadeout(0.3)
        elif animation == 'slide':
            clip = clip.set_position(lambda t: (-self.width + int(t * self.width / 0.5), 'center') if t < 0.5 else ('center', 'center'))
        # 暂时禁用zoom动画以避免PIL兼容性问题
        # elif animation == 'zoom':
        #     if is_highlighted:
        #         clip = clip.resize(lambda t: 0.8 + 0.2 * min(t / 0.3, 1))
        
        return clip

    # --- BEGIN PRIVATE HELPER METHODS ---
    def _create_video_background(
        self, 
        duration: float, 
        background_image_path: Optional[str] = None
    ) -> ImageClip:
        """(Helper) 创建视频背景片段（图片或纯黑）。"""
        if background_image_path and os.path.exists(background_image_path):
            bg_array = self.load_background_image(background_image_path)
            if bg_array is not None:
                print(f"   使用背景图片: {background_image_path}")
                return ImageClip(bg_array, duration=duration)
            else:
                print("   背景图片加载失败，使用纯黑背景替代。")
                return ColorClip(size=(self.width, self.height), color=(0,0,0), duration=duration)
        else:
            if background_image_path:
                print(f"   背景图片路径不存在: {background_image_path}。使用纯黑背景。")
            else:
                print("   未使用背景图片，使用纯黑背景。")
            return ColorClip(size=(self.width, self.height), color=(0,0,0), duration=duration)

    def _generate_lyric_clips(
        self, 
        lyrics_list: List[Tuple[float, str]], 
        total_video_duration: float,
        y_position: int,
        is_highlighted: bool,
        animation_style: str = 'fade'
    ) -> List[ImageClip]:
        """(Helper) 为单个语言的歌词列表生成视频片段列表。"""
        clips = []
        for i, (start_time, text) in enumerate(lyrics_list):
            if i < len(lyrics_list) - 1:
                end_time = lyrics_list[i + 1][0]
            else:
                end_time = total_video_duration
            
            lyric_duration = end_time - start_time
            if lyric_duration <= 0.01:
                print(f"   跳过歌词（时长过短 {lyric_duration:.2f}s）: '{text}'")
                continue

            lyric_clip = self.create_lyric_clip_with_animation(
                text, start_time, lyric_duration,
                is_highlighted=is_highlighted,
                y_position=y_position,
                animation=animation_style
            )
            clips.append(lyric_clip)
        return clips

    def _finalize_and_export_video(
        self,
        all_clips: List[ImageClip],
        audio_clip: AudioFileClip,
        output_path: str,
        temp_audio_file_suffix: str = "generic",
        ffmpeg_params_custom: Optional[List[str]] = None
    ):
        """(Helper) 合成所有片段并导出视频。"""
        print("🎞️  合成视频...")
        final_video = CompositeVideoClip(all_clips)
        final_video = final_video.set_audio(audio_clip)
        final_video = final_video.set_fps(self.fps)
        
        temp_audio_filename = f'temp-audio-{temp_audio_file_suffix}-{hash(output_path) % 10000}.m4a'
        
        actual_ffmpeg_params = ffmpeg_params_custom if ffmpeg_params_custom is not None else ['-crf', '18']
        
        print(f"💾 导出视频到: {output_path}")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile=temp_audio_filename,
            remove_temp=True,
            verbose=False, 
            logger=None, 
            preset='medium', 
            ffmpeg_params=actual_ffmpeg_params        )
    # --- END PRIVATE HELPER METHODS ---    

    def generate_enhanced_video(self, main_lyrics: List[Tuple[float, str]], 
                              audio_path: str, output_path: str,
                              background_image: Optional[str] = None,
                              animation_style: str = 'fade',
                              t_max_sec: float = float('inf')) -> bool:
        """生成增强版歌词视频 (部分重构)"""
        try:
            print(f"🎬 开始生成增强版: {output_path}")
            
            print("📻 加载音频...")
            audio = AudioFileClip(audio_path)
            original_duration = audio.duration
            duration = min(original_duration, t_max_sec)
            
            # 如果需要裁剪音频
            if t_max_sec < original_duration:
                audio = audio.subclip(0, t_max_sec)
                print(f"   音频已裁剪: {original_duration:.1f}s -> {duration:.1f}s")
            else:
                print(f"   音频时长: {duration:.1f} 秒")
            
            # 过滤歌词，只保留在时间范围内的
            filtered_lyrics = [(t, text) for t, text in main_lyrics if t < duration]
            print(f"   使用歌词行数: {len(filtered_lyrics)}/{len(main_lyrics)}")
            
            print("🎨 创建背景...")
            background_clip = self._create_video_background(duration, background_image)
            
            all_video_clips = [background_clip]
            
            print("📝 创建歌词片段...")
            lyric_clips_generated = 0
            for i, (start_time, text) in enumerate(filtered_lyrics):
                if i < len(filtered_lyrics) - 1:
                    end_time = filtered_lyrics[i + 1][0]
                else:
                    end_time = duration
                
                lyric_duration = end_time - start_time
                if lyric_duration <= 0.01:
                    print(f"   跳过主歌词（时长过短 {lyric_duration:.2f}s）: '{text}'")
                    continue

                current_clip = self.create_lyric_clip_with_animation(
                    text, start_time, lyric_duration,
                    is_highlighted=True,
                    y_position=self.height // 2 - 50,
                    animation=animation_style
                )
                all_video_clips.append(current_clip)
                lyric_clips_generated += 1
                
                if i < len(filtered_lyrics) - 1:
                    next_text = filtered_lyrics[i + 1][1]
                    next_clip = self.create_lyric_clip_with_animation(
                        next_text, start_time, lyric_duration, 
                        is_highlighted=False,
                        y_position=self.height // 2 + 80,
                        animation='fade'
                    )
                    all_video_clips.append(next_clip)
                    lyric_clips_generated +=1
            
            print(f"   创建了 {lyric_clips_generated} 个歌词片段 (总共 {len(all_video_clips)} 个视频片段包括背景)")
            
            self._finalize_and_export_video(
                all_clips=all_video_clips,
                audio_clip=audio,
                output_path=output_path,
                temp_audio_file_suffix="enhanced"
            )
            
            print("✅ 增强版视频生成成功！")
            return True
            
        except Exception as e:
            print(f"❌ 增强版生成失败: {e}")
            traceback.print_exc()
            return False
    def generate_bilingual_video(self, main_lyrics: List[Tuple[float, str]],
                               aux_lyrics: List[Tuple[float, str]],
                               audio_path: str, output_path: str,
                               background_image: Optional[str] = None,
                               t_max_sec: float = float('inf')) -> bool:
        """生成双语版本视频 (重构版)"""
        try:
            print(f"🎬 开始生成双语版: {output_path}")
            
            print("📻 加载音频...")
            audio = AudioFileClip(audio_path)
            original_duration = audio.duration
            duration = min(original_duration, t_max_sec)
            
            # 如果需要裁剪音频
            if t_max_sec < original_duration:
                audio = audio.subclip(0, t_max_sec)
                print(f"   音频已裁剪: {original_duration:.1f}s -> {duration:.1f}s")
            else:
                print(f"   音频时长: {duration:.1f} 秒")
              # 过滤歌词，只保留在时间范围内的
            main_lyrics = [(t, text) for t, text in main_lyrics if t < duration]
            aux_lyrics = [(t, text) for t, text in aux_lyrics if t < duration]
            print(f"   使用中文歌词行数: {len(main_lyrics)}")
            print(f"   使用英文歌词行数: {len(aux_lyrics)}")
            
            print("🎨 创建背景...")
            background_clip = self._create_video_background(duration, background_image)
            
            all_video_clips = [background_clip]
            
            print("📝 处理中文歌词...")
            chinese_clips_list = self._generate_lyric_clips(
                lyrics_list=main_lyrics,
                total_video_duration=duration,
                y_position=self.height // 2 - 80,
                is_highlighted=True,
                animation_style='fade'
            )
            all_video_clips.extend(chinese_clips_list)
            
            print("📝 处理英文歌词...")
            english_clips_list = self._generate_lyric_clips(
                lyrics_list=aux_lyrics,
                total_video_duration=duration,
                y_position=self.height // 2 + 60,
                is_highlighted=False,
                animation_style='fade'
            )
            all_video_clips.extend(english_clips_list)
            
            num_lyric_clips = len(chinese_clips_list) + len(english_clips_list)
            print(f"   创建了 {num_lyric_clips} 个歌词片段 (总共 {len(all_video_clips)} 个视频片段包括背景)")
            
            self._finalize_and_export_video(
                all_clips=all_video_clips,
                audio_clip=audio,
                output_path=output_path,
                temp_audio_file_suffix="bilingual"
            )
            
            print("✅ 双语版视频生成成功！")
            return True
            
        except Exception as e:
            print(f"❌ 双语版生成失败: {e}")
            traceback.print_exc()
            return False

def demo_enhanced_features(config_path: Path, t_max_sec: float = float('inf')):
    """使用配置文件生成歌词视频"""
    print("🎬 精武英雄歌词视频生成器 - 配置驱动版")
    print("=" * 50)
    
    try:
        # 加载配置文件
        print(f"📁 加载配置文件: {config_path}")
        config = load_lrc_mv_config(str(config_path))
        print("✅ 配置文件加载成功")
        
        # 显示配置信息
        print(f"   音频文件: {config.audio}")
        print(f"   主歌词: {config.main_lrc.path} ({config.main_lrc.lang})")
        if config.aux_lrc:
            print(f"   副歌词: {config.aux_lrc.path} ({config.aux_lrc.lang})")
        print(f"   背景图片: {config.background}")
        print(f"   输出尺寸: {config.width}x{config.height}")
        print(f"   输出文件: {config.output}")
        
        # 验证文件存在性
        print("\n🔍 验证文件存在性...")
        config.validate_files()
        print("✅ 所有必需文件都存在")
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False
    
    # 创建生成器，使用配置文件中的尺寸
    generator = EnhancedJingwuGenerator(width=config.width, height=config.height, fps=DEFAULT_FPS)
    generator.default_font_size = 60
    print("\n🎯 开始生成视频...")
    
    # 解析主歌词文件
    main_lrc_path = config.get_main_lrc_path()
    main_lyrics = generator.parse_lrc_file(str(main_lrc_path))
    
    # 应用时间限制
    if t_max_sec < float('inf'):
        main_lyrics = [(t, text) for t, text in main_lyrics]
        print(f"📖 使用歌词（前{t_max_sec:.1f}秒，共{len(main_lyrics)}行）")
    else:
        print(f"📖 使用完整歌词（共{len(main_lyrics)}行）")
    
    # 获取路径
    audio_path = config.get_audio_path()
    background_path = config.get_background_path()
    output_path = config.get_output_path()
    
    # 根据配置决定生成类型
    if config.aux_lrc:
        # 生成双语版本
        print("\n🌍 生成双语版本...")
        aux_lrc_path = config.get_aux_lrc_path()
        aux_lyrics = generator.parse_lrc_file(str(aux_lrc_path))
        
        # 应用时间限制
        aux_lyrics = [(t, text) for t, text in aux_lyrics]
        success = generator.generate_bilingual_video(
        main_lyrics=main_lyrics,
        aux_lyrics=aux_lyrics,
        audio_path=str(audio_path),
        output_path=str(output_path),
        background_image=str(background_path),
        t_max_sec=t_max_sec
        )
    else:
        # 生成单语增强版本
        print("\n🎨 生成增强版本...")
        success = generator.generate_enhanced_video(
            main_lyrics=main_lyrics,
            audio_path=str(audio_path),
            output_path=str(output_path),
            background_image=str(background_path),
            animation_style="fade",
            t_max_sec=t_max_sec
        )
    
    if success:
        print(f"\n✅ 视频生成成功！")
        print(f"📁 输出文件: {output_path}")
        if output_path.exists():
            file_size = output_path.stat().st_size / (1024 * 1024)  # MB
            print(f"📊 文件大小: {file_size:.1f} MB")
    else:
        print(f"\n❌ 视频生成失败！")
    
    return success

if __name__ == "__main__":
    demo_enhanced_features(Path(r"精武英雄\lrc-mv.yaml"), t_max_sec=60.0)
