"""
歌词时间轴类型设计方案
实现更面向对象的歌词处理和显示
"""

from abc import ABC, abstractmethod
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum
import numpy as np
from moviepy.editor import ImageClip

# 显示模式枚举
class LyricDisplayMode(Enum):
    SIMPLE_FADE = "simple_fade"           # 简单淡入淡出
    ENHANCED_PREVIEW = "enhanced_preview"  # 增强模式：当前+预览
    BILINGUAL_SYNC = "bilingual_sync"     # 双语同步显示
    KARAOKE_STYLE = "karaoke_style"       # 卡拉OK样式（未来扩展）

# 位置和尺寸信息
@dataclass
class LyricRect:
    """歌词显示区域信息"""
    x: int
    y: int
    width: int
    height: int
    
    def __post_init__(self):
        """验证尺寸参数"""
        if self.width <= 0 or self.height <= 0:
            raise ValueError("宽度和高度必须大于0")

@dataclass
class LyricStyle:
    """歌词样式配置"""
    font_size: int = 80
    font_color: str = 'white'
    highlight_color: str = '#FFD700'
    shadow_color: Tuple[int, int, int, int] = (0, 0, 0, 200)
    glow_enabled: bool = False
    animation_style: str = 'fade'

# 抽象基类：歌词显示策略
class LyricDisplayStrategy(ABC):
    """歌词显示策略抽象基类"""
    
    @abstractmethod
    def calculate_required_rect(self, timeline: 'LyricTimeline', 
                              video_width: int, video_height: int) -> LyricRect:
        """计算所需的显示区域"""
        pass
    
    @abstractmethod
    def generate_clips(self, timeline: 'LyricTimeline', 
                      generator: Any, duration: float) -> List[ImageClip]:
        """生成歌词视频片段"""
        pass

# 具体策略：简单淡入淡出
class SimpleFadeStrategy(LyricDisplayStrategy):
    """简单淡入淡出显示策略"""
    
    def __init__(self, y_position: Optional[int] = None, is_highlighted: bool = True):
        self.y_position = y_position
        self.is_highlighted = is_highlighted
    
    def calculate_required_rect(self, timeline: 'LyricTimeline', 
                              video_width: int, video_height: int) -> LyricRect:
        """计算简单显示所需区域"""
        y_pos = self.y_position or (video_height // 2)
        font_size = timeline.style.font_size
        
        # 估算文字高度（字体大小 * 1.2 作为行高）
        text_height = int(font_size * 1.2)
        
        return LyricRect(
            x=0,
            y=y_pos - text_height // 2,
            width=video_width,
            height=text_height
        )
    
    def generate_clips(self, timeline: 'LyricTimeline', 
                      generator: Any, duration: float) -> List[ImageClip]:
        """生成简单淡入淡出片段"""
        clips = []
        lyrics_data = timeline.get_filtered_lyrics(duration)
        rect = self.calculate_required_rect(timeline, generator.width, generator.height)
        
        for i, (start_time, text) in enumerate(lyrics_data):
            if i < len(lyrics_data) - 1:
                end_time = lyrics_data[i + 1][0]
            else:
                end_time = duration
            
            lyric_duration = end_time - start_time
            if lyric_duration <= 0.01:
                continue
            
            clip = generator.create_lyric_clip_with_animation(
                text, start_time, lyric_duration,
                is_highlighted=self.is_highlighted,
                y_position=rect.y + rect.height // 2,
                animation=timeline.style.animation_style
            )
            clips.append(clip)
        
        return clips

# 具体策略：增强预览模式
class EnhancedPreviewStrategy(LyricDisplayStrategy):
    """增强预览模式：当前歌词+下一句预览"""
    
    def __init__(self, current_y_offset: int = -50, preview_y_offset: int = 80):
        self.current_y_offset = current_y_offset
        self.preview_y_offset = preview_y_offset
    
    def calculate_required_rect(self, timeline: 'LyricTimeline', 
                              video_width: int, video_height: int) -> LyricRect:
        """计算增强预览所需区域"""
        font_size = timeline.style.font_size
        text_height = int(font_size * 1.2)
        
        # 需要容纳当前歌词和预览歌词
        total_height = abs(self.current_y_offset) + abs(self.preview_y_offset) + text_height * 2
        center_y = video_height // 2
        
        return LyricRect(
            x=0,
            y=center_y - total_height // 2,
            width=video_width,
            height=total_height
        )
    
    def generate_clips(self, timeline: 'LyricTimeline', 
                      generator: Any, duration: float) -> List[ImageClip]:
        """生成增强预览片段"""
        clips = []
        lyrics_data = timeline.get_filtered_lyrics(duration)
        center_y = generator.height // 2
        
        for i, (start_time, text) in enumerate(lyrics_data):
            if i < len(lyrics_data) - 1:
                end_time = lyrics_data[i + 1][0]
            else:
                end_time = duration
            
            lyric_duration = end_time - start_time
            if lyric_duration <= 0.01:
                continue
            
            # 当前歌词（高亮）
            current_clip = generator.create_lyric_clip_with_animation(
                text, start_time, lyric_duration,
                is_highlighted=True,
                y_position=center_y + self.current_y_offset,
                animation=timeline.style.animation_style
            )
            clips.append(current_clip)
            
            # 下一句预览（非高亮）
            if i < len(lyrics_data) - 1:
                next_text = lyrics_data[i + 1][1]
                preview_clip = generator.create_lyric_clip_with_animation(
                    next_text, start_time, lyric_duration,
                    is_highlighted=False,
                    y_position=center_y + self.preview_y_offset,
                    animation='fade'
                )
                clips.append(preview_clip)
        
        return clips

# 主要的歌词时间轴类
class LyricTimeline:
    """歌词时间轴类 - 封装歌词数据和显示逻辑"""
    
    def __init__(self, lyrics_data: List[Tuple[float, str]], 
                 language: str = "unknown",
                 style: Optional[LyricStyle] = None,
                 display_mode: LyricDisplayMode = LyricDisplayMode.SIMPLE_FADE):
        self.lyrics_data = lyrics_data
        self.language = language
        self.style = style or LyricStyle()
        self.display_mode = display_mode
        self._strategy: Optional[LyricDisplayStrategy] = None
        self._setup_strategy()
    
    def _setup_strategy(self):
        """根据显示模式设置策略"""
        if self.display_mode == LyricDisplayMode.SIMPLE_FADE:
            self._strategy = SimpleFadeStrategy()
        elif self.display_mode == LyricDisplayMode.ENHANCED_PREVIEW:
            self._strategy = EnhancedPreviewStrategy()
        else:
            raise ValueError(f"不支持的显示模式: {self.display_mode}")
    
    def set_display_mode(self, mode: LyricDisplayMode, **kwargs):
        """设置显示模式"""
        self.display_mode = mode
        if mode == LyricDisplayMode.SIMPLE_FADE:
            self._strategy = SimpleFadeStrategy(**kwargs)
        elif mode == LyricDisplayMode.ENHANCED_PREVIEW:
            self._strategy = EnhancedPreviewStrategy(**kwargs)
        else:
            raise ValueError(f"不支持的显示模式: {mode}")
    
    def get_filtered_lyrics(self, max_duration: float) -> List[Tuple[float, str]]:
        """获取过滤后的歌词数据"""
        return [(t, text) for t, text in self.lyrics_data if t < max_duration]
    
    def calculate_required_rect(self, video_width: int, video_height: int) -> LyricRect:
        """计算所需的显示区域"""
        if not self._strategy:
            raise ValueError("显示策略未设置")
        return self._strategy.calculate_required_rect(self, video_width, video_height)
    
    def generate_clips(self, generator: Any, duration: float) -> List[ImageClip]:
        """生成视频片段"""
        if not self._strategy:
            raise ValueError("显示策略未设置")
        return self._strategy.generate_clips(self, generator, duration)
    
    def get_info(self) -> Dict[str, Any]:
        """获取时间轴信息"""
        return {
            "language": self.language,
            "total_lines": len(self.lyrics_data),
            "duration": self.lyrics_data[-1][0] if self.lyrics_data else 0,
            "display_mode": self.display_mode.value,
            "style": self.style
        }
    
    @classmethod
    def from_lrc_file(cls, lrc_path: str, language: str = "unknown", 
                     display_mode: LyricDisplayMode = LyricDisplayMode.SIMPLE_FADE) -> 'LyricTimeline':
        """从LRC文件创建时间轴"""
        # 这里需要实现LRC解析逻辑，或者调用现有的解析方法
        # 暂时返回空实例作为示例
        return cls([], language, display_mode=display_mode)

# 使用示例
def usage_example():
    """使用示例"""
    # 创建中文歌词时间轴（增强预览模式）
    chinese_timeline = LyricTimeline(
        lyrics_data=[(0.0, "第一句歌词"), (3.0, "第二句歌词")],
        language="chinese",
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW
    )
    
    # 创建英文歌词时间轴（简单淡入淡出）
    english_timeline = LyricTimeline(
        lyrics_data=[(0.0, "First line"), (3.0, "Second line")],
        language="english", 
        display_mode=LyricDisplayMode.SIMPLE_FADE
    )
    
    # 获取所需显示区域
    chinese_rect = chinese_timeline.calculate_required_rect(1280, 720)
    english_rect = english_timeline.calculate_required_rect(1280, 720)
    
    print(f"中文歌词区域: {chinese_rect}")
    print(f"英文歌词区域: {english_rect}")
    
    # 在生成器中使用
    # clips = chinese_timeline.generate_clips(generator, duration=30.0)

if __name__ == "__main__":
    usage_example()
