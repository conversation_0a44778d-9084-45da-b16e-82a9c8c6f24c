"""
精武英雄歌词视频生成器
专门为甄子丹《精武英雄》创建歌词视频
"""

from fixed_lyric_generator import FixedLyricVideoGenerator
import os
from PIL import Image, ImageDraw, ImageFont
import numpy as np


def create_martial_arts_background():
    """创建武术风格的背景图片"""
    print("创建精武英雄主题背景...")
    
    width, height = 1920, 1080
    image = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(image)
    
    # 创建深色渐变背景（深红到黑色）
    for y in range(height):
        ratio = y / height
        # 深红(139, 0, 0) 到 黑色(0, 0, 0)
        r = int(139 * (1 - ratio))
        g = int(0)
        b = int(0)
        
        color = (r, g, b)
        draw.line([(0, y), (width, y)], fill=color)
    
    # 添加一些装饰性的几何图案（模拟传统纹样）
    # 绘制一些圆形和线条作为装饰
    for i in range(15):
        x = np.random.randint(100, width-100)
        y = np.random.randint(100, height-100)
        radius = np.random.randint(30, 120)
        
        # 半透明的金色圆圈
        alpha = np.random.randint(20, 60)
        circle_color = (255, 215, 0, alpha)  # 金色
        
        # 由于PIL的draw不直接支持alpha，我们画一个较淡的颜色
        light_color = (int(255*alpha/255), int(215*alpha/255), int(0*alpha/255))
        draw.ellipse([x-radius, y-radius, x+radius, y+radius], 
                    outline=light_color, width=2)
    
    # 添加标题区域
    try:
        # 尝试使用系统字体
        title_font = ImageFont.truetype("simsun.ttc", 72)  # 宋体
        subtitle_font = ImageFont.truetype("arial.ttf", 48)
    except:
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
    
    # 添加标题文字
    title = "精武英雄"
    subtitle = "Jingwu Hero"
    
    # 计算文字位置（居中）
    title_bbox = draw.textbbox((0, 0), title, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (width - title_width) // 2
    title_y = 100
    
    subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    subtitle_x = (width - subtitle_width) // 2
    subtitle_y = title_y + 100
    
    # 添加文字阴影
    shadow_offset = 3
    draw.text((title_x + shadow_offset, title_y + shadow_offset), title, 
              fill=(0, 0, 0), font=title_font)
    draw.text((subtitle_x + shadow_offset, subtitle_y + shadow_offset), subtitle, 
              fill=(0, 0, 0), font=subtitle_font)
    
    # 添加主文字（金色）
    draw.text((title_x, title_y), title, fill=(255, 215, 0), font=title_font)
    draw.text((subtitle_x, subtitle_y), subtitle, fill=(255, 215, 0), font=subtitle_font)
    
    image.save('jingwu_hero_background.jpg', 'JPEG', quality=95)
    print("✅ 精武英雄背景已创建: jingwu_hero_background.jpg")


def create_jingwu_hero_video():
    """生成精武英雄歌词视频（中文版）"""
    print("\n=== 生成精武英雄歌词视频（中文版） ===")
    
    # 检查必要文件
    audio_file = "精武英雄 - 甄子丹.flac"
    lyrics_file = "精武英雄 - 甄子丹.lrc"
    
    if not os.path.exists(audio_file):
        print(f"❌ 音频文件不存在: {audio_file}")
        return False
        
    if not os.path.exists(lyrics_file):
        print(f"❌ 歌词文件不存在: {lyrics_file}")
        return False
    
    # 创建背景
    create_martial_arts_background()
    
    # 创建生成器
    generator = LyricVideoGenerator(width=1920, height=1080, fps=30)
    
    # 自定义武术风格样式
    generator.default_font_size = 72
    generator.default_font_color = 'white'
    generator.highlight_color = '#FFD700'  # 金色高亮
    
    # 解析中文歌词
    print("解析中文歌词...")
    lyrics = generator.parse_lrc_file(lyrics_file)
    
    print("找到歌词行数:", len(lyrics))
    for i, (timestamp, text) in enumerate(lyrics[:10]):  # 显示前10行
        print(f"  {timestamp:6.1f}s: {text}")
    if len(lyrics) > 10:
        print(f"  ... 还有 {len(lyrics) - 10} 行歌词")
    
    try:
        print("\n开始生成中文版视频...")
        generator.generate_video(
            lyrics=lyrics,
            audio_path=audio_file,
            background_source='jingwu_hero_background.jpg',
            output_path='精武英雄_中文版.mp4',
            animation='fade',
            show_current_and_next=True
        )
        print("✅ 中文版视频生成成功: 精武英雄_中文版.mp4")
        return True
        
    except Exception as e:
        print(f"❌ 中文版视频生成失败: {e}")
        return False


def create_english_version():
    """生成英文版歌词视频"""
    print("\n=== 生成精武英雄歌词视频（英文版） ===")
    
    # 检查英文歌词文件
    english_lyrics_file = "Jingwu Hero - Donnie Yen (English).lrc"
    audio_file = "精武英雄 - 甄子丹.flac"
    
    if not os.path.exists(english_lyrics_file):
        print(f"❌ 英文歌词文件不存在: {english_lyrics_file}")
        return False
    
    # 创建生成器
    generator = LyricVideoGenerator(width=1920, height=1080, fps=30)
    
    # 英文版样式设置
    generator.default_font_size = 64
    generator.default_font_color = 'white'
    generator.highlight_color = '#87CEEB'  # 天蓝色高亮
    
    # 解析英文歌词
    print("解析英文歌词...")
    lyrics = generator.parse_lrc_file(english_lyrics_file)
    
    print("找到英文歌词行数:", len(lyrics))
    for i, (timestamp, text) in enumerate(lyrics[:8]):  # 显示前8行
        print(f"  {timestamp:6.1f}s: {text}")
    if len(lyrics) > 8:
        print(f"  ... 还有 {len(lyrics) - 8} 行歌词")
    
    try:
        print("\n开始生成英文版视频...")
        generator.generate_video(
            lyrics=lyrics,
            audio_path=audio_file,
            background_source='jingwu_hero_background.jpg',
            output_path='Jingwu_Hero_English.mp4',
            animation='slide',
            show_current_and_next=True
        )
        print("✅ 英文版视频生成成功: Jingwu_Hero_English.mp4")
        return True
        
    except Exception as e:
        print(f"❌ 英文版视频生成失败: {e}")
        return False


def create_bilingual_version():
    """创建中英双语版本"""
    print("\n=== 创建中英双语版本 ===")
    
    # 这个功能需要更复杂的实现，暂时提供思路
    print("双语版本需要:")
    print("1. 同时解析中英文歌词")
    print("2. 对齐时间戳")
    print("3. 在视频中同时显示中英文")
    print("4. 调整布局以适应双语显示")
    
    print("\n建议的实现方案:")
    print("- 中文歌词显示在上方")
    print("- 英文歌词显示在下方")
    print("- 使用不同的字体大小和颜色区分")
    
    # TODO: 实现双语版本
    print("⏳ 双语版本功能开发中...")


def test_with_sample():
    """使用简短的示例进行测试"""
    print("\n=== 快速测试（使用前30秒） ===")
    
    audio_file = "精武英雄 - 甄子丹.flac"
    lyrics_file = "精武英雄 - 甄子丹.lrc"
    
    if not os.path.exists(audio_file) or not os.path.exists(lyrics_file):
        print("❌ 测试需要音频和歌词文件")
        return False
    
    # 创建简化背景
    create_martial_arts_background()
    
    # 创建生成器
    generator = LyricVideoGenerator(width=1280, height=720, fps=24)
    generator.default_font_size = 60
    generator.highlight_color = '#FFD700'
    
    # 解析歌词并只取前几行
    all_lyrics = generator.parse_lrc_file(lyrics_file)
    
    # 只使用前30秒的歌词进行测试
    test_lyrics = [(t, text) for t, text in all_lyrics if t <= 30.0]
    
    print(f"测试歌词（前30秒，共{len(test_lyrics)}行）:")
    for timestamp, text in test_lyrics:
        print(f"  {timestamp:6.1f}s: {text}")
    
    if not test_lyrics:
        print("❌ 前30秒没有找到歌词")
        return False
    
    try:
        print("\n开始生成测试视频...")
        # 注意：这里需要手动截取音频的前30秒
        # 由于MoviePy的限制，我们生成完整视频但建议用户后续手动裁剪
        generator.generate_video(
            lyrics=test_lyrics,
            audio_path=audio_file,
            background_source='jingwu_hero_background.jpg',
            output_path='精武英雄_测试版.mp4',
            animation='fade',
            show_current_and_next=False  # 简化显示
        )
        print("✅ 测试视频生成成功: 精武英雄_测试版.mp4")
        print("💡 提示: 视频包含完整音频，可以手动裁剪前30秒进行测试")
        return True
        
    except Exception as e:
        print(f"❌ 测试视频生成失败: {e}")
        return False


def main():
    """主函数"""
    print("🥋 精武英雄歌词视频生成器")
    print("=" * 60)
    
    # 显示可用文件
    print("📁 检查项目文件:")
    files_to_check = [
        "精武英雄 - 甄子丹.flac",
        "精武英雄 - 甄子丹.lrc", 
        "Jingwu Hero - Donnie Yen (English).lrc"
    ]
    
    for file in files_to_check:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file}")
    
    print("\n🎬 可生成的视频版本:")
    print("1. 中文版 - 原版中文歌词")
    print("2. 英文版 - AI翻译英文歌词")
    print("3. 测试版 - 快速测试（推荐先用这个）")
    print("4. 双语版 - 中英对照（开发中）")
    
    # 询问用户选择
    print("\n🚀 开始生成...")
    print("建议流程:")
    print("1. 先运行测试版验证效果")
    print("2. 再生成完整版本")
    
    # 自动运行测试版
    print("\n" + "=" * 60)
    success = test_with_sample()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 测试成功！现在可以生成完整版本:")
        print("\n若要生成完整中文版，运行:")
        print("python -c \"from jingwu_hero_generator import create_jingwu_hero_video; create_jingwu_hero_video()\"")
        print("\n若要生成英文版，运行:")
        print("python -c \"from jingwu_hero_generator import create_english_version; create_english_version()\"")
    
    print("\n📝 生成说明:")
    print("- 视频分辨率: 1920x1080 (可在代码中调整)")
    print("- 字体样式: 金色高亮，武术风格背景")
    print("- 动画效果: 淡入淡出/滑动进入")
    print("- 输出格式: MP4 (H.264 + AAC)")


if __name__ == "__main__":
    main()
